import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import org.gradle.api.tasks.testing.logging.TestLogEvent.*
import org.gradle.kotlin.dsl.annotationProcessor

plugins {
  java
  application
  id("com.github.johnrengelman.shadow") version "8.1.1"
}

group = "com.fast.core"


val vertxVersion = "5.0.1"
val jimmerVersion = "0.9.99"
val junitJupiterVersion = "5.9.1"

val mainVerticleName = "com.fast.basic.MainVerticle"
val launcherClassName = "io.vertx.launcher.application.VertxApplication"

application {
  mainClass.set(launcherClassName)
}

dependencies {
  implementation(platform("io.vertx:vertx-stack-depchain:$vertxVersion"))
  implementation("io.vertx:vertx-launcher-application")
  implementation("io.vertx:vertx-web-validation")
  implementation("io.vertx:vertx-web")
  implementation("io.vertx:vertx-pg-client")
  implementation("io.vertx:vertx-jdbc-client")
  implementation("com.ongres.scram:client:2.1")
  implementation("com.graphql-java:graphql-java:24.1")
  implementation("com.graphql-java:graphql-java-extended-scalars:24.0")



  implementation("org.babyfish.jimmer:jimmer-sql:${jimmerVersion}")

  annotationProcessor("org.babyfish.jimmer:jimmer-apt:${jimmerVersion}")

  testImplementation("io.vertx:vertx-junit5")
  testImplementation("org.junit.jupiter:junit-jupiter:$junitJupiterVersion")
}

java {
  sourceCompatibility = JavaVersion.VERSION_17
  targetCompatibility = JavaVersion.VERSION_17
}


tasks.withType<ShadowJar> {
  archiveClassifier.set("fat")
  manifest {
    attributes(mapOf("Main-Verticle" to mainVerticleName))
  }
  mergeServiceFiles()
}

tasks.withType<Test> {
  useJUnitPlatform()
  testLogging {
    events = setOf(PASSED, SKIPPED, FAILED)
  }
}

tasks.withType<JavaExec> {
  args = listOf(mainVerticleName)
}
