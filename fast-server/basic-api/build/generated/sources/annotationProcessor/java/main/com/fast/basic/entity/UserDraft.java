package com.fast.basic.entity;

import com.fast.basic.entity.type.UserStateType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.Collections;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.Draft;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.Visibility;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@GeneratedBy(
        type = User.class
)
public interface UserDraft extends User, Draft {
    UserDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    UserDraft setId(long id);

    @OldChain
    UserDraft setUsername(String username);

    @OldChain
    UserDraft setPassword(String password);

    @OldChain
    UserDraft setName(String name);

    @OldChain
    UserDraft setEmail(String email);

    @OldChain
    UserDraft setSex(String sex);

    @OldChain
    UserDraft setNation(String nation);

    @OldChain
    UserDraft setBirthplace(String birthplace);

    @OldChain
    UserDraft setBirthdate(String birthdate);

    @OldChain
    UserDraft setIdCard(String idCard);

    @OldChain
    UserDraft setMobile(String mobile);

    @OldChain
    UserDraft setState(UserStateType state);

    @OldChain
    UserDraft setOrgId(String orgId);

    @GeneratedBy(
            type = User.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 0;

        public static final int SLOT_USERNAME = 1;

        public static final int SLOT_PASSWORD = 2;

        public static final int SLOT_NAME = 3;

        public static final int SLOT_EMAIL = 4;

        public static final int SLOT_SEX = 5;

        public static final int SLOT_NATION = 6;

        public static final int SLOT_BIRTHPLACE = 7;

        public static final int SLOT_BIRTHDATE = 8;

        public static final int SLOT_ID_CARD = 9;

        public static final int SLOT_MOBILE = 10;

        public static final int SLOT_STATE = 11;

        public static final int SLOT_ORG_ID = 12;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.9.99",
                User.class,
                Collections.emptyList(),
                (ctx, base) -> new DraftImpl(ctx, (User)base)
            )
            .id(SLOT_ID, "id", long.class)
            .key(SLOT_USERNAME, "username", String.class, false)
            .key(SLOT_PASSWORD, "password", String.class, false)
            .key(SLOT_NAME, "name", String.class, false)
            .key(SLOT_EMAIL, "email", String.class, false)
            .key(SLOT_SEX, "sex", String.class, false)
            .key(SLOT_NATION, "nation", String.class, false)
            .key(SLOT_BIRTHPLACE, "birthplace", String.class, false)
            .key(SLOT_BIRTHDATE, "birthdate", String.class, false)
            .key(SLOT_ID_CARD, "idCard", String.class, false)
            .key(SLOT_MOBILE, "mobile", String.class, false)
            .add(SLOT_STATE, "state", ImmutablePropCategory.SCALAR, UserStateType.class, false)
            .key(SLOT_ORG_ID, "orgId", String.class, false)
            .build();

        private Producer() {
        }

        public User produce(DraftConsumer<UserDraft> block) {
            return (User)Internal.produce(TYPE, null, block);
        }

        public User produce(User base, DraftConsumer<UserDraft> block) {
            return (User)Internal.produce(TYPE, base, block);
        }

        public User produce(boolean resolveImmediately, DraftConsumer<UserDraft> block) {
            return (User)Internal.produce(TYPE, null, resolveImmediately, block);
        }

        public User produce(User base, boolean resolveImmediately, DraftConsumer<UserDraft> block) {
            return (User)Internal.produce(TYPE, base, resolveImmediately, block);
        }

        /**
         * Class, not interface, for free-marker
         */
        @GeneratedBy(
                type = User.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "id", "username", "password", "name", "email", "sex", "nation", "birthplace", "birthdate", "idCard", "mobile", "state", "orgId"})
        public abstract static class Implementor implements User, ImmutableSpi {
            @Override
            public final Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_USERNAME:
                    		return username();
                    case SLOT_PASSWORD:
                    		return password();
                    case SLOT_NAME:
                    		return name();
                    case SLOT_EMAIL:
                    		return email();
                    case SLOT_SEX:
                    		return sex();
                    case SLOT_NATION:
                    		return nation();
                    case SLOT_BIRTHPLACE:
                    		return birthplace();
                    case SLOT_BIRTHDATE:
                    		return birthdate();
                    case SLOT_ID_CARD:
                    		return idCard();
                    case SLOT_MOBILE:
                    		return mobile();
                    case SLOT_STATE:
                    		return state();
                    case SLOT_ORG_ID:
                    		return orgId();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.fast.basic.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            public final Object __get(String prop) {
                switch (prop) {
                    case "id":
                    		return (Long)id();
                    case "username":
                    		return username();
                    case "password":
                    		return password();
                    case "name":
                    		return name();
                    case "email":
                    		return email();
                    case "sex":
                    		return sex();
                    case "nation":
                    		return nation();
                    case "birthplace":
                    		return birthplace();
                    case "birthdate":
                    		return birthdate();
                    case "idCard":
                    		return idCard();
                    case "mobile":
                    		return mobile();
                    case "state":
                    		return state();
                    case "orgId":
                    		return orgId();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.fast.basic.entity.User\": \"" + prop + "\"");
                }
            }

            public final long getId() {
                return id();
            }

            public final String getUsername() {
                return username();
            }

            public final String getPassword() {
                return password();
            }

            public final String getName() {
                return name();
            }

            public final String getEmail() {
                return email();
            }

            public final String getSex() {
                return sex();
            }

            public final String getNation() {
                return nation();
            }

            public final String getBirthplace() {
                return birthplace();
            }

            public final String getBirthdate() {
                return birthdate();
            }

            public final String getIdCard() {
                return idCard();
            }

            public final String getMobile() {
                return mobile();
            }

            public final UserStateType getState() {
                return state();
            }

            public final String getOrgId() {
                return orgId();
            }

            @Override
            public final ImmutableType __type() {
                return TYPE;
            }

            public final int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = User.class
        )
        private static class Impl extends Implementor implements Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            String __usernameValue;

            String __passwordValue;

            String __nameValue;

            String __emailValue;

            String __sexValue;

            String __nationValue;

            String __birthplaceValue;

            String __birthdateValue;

            String __idCardValue;

            String __mobileValue;

            UserStateType __stateValue;

            String __orgIdValue;

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(User.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public String username() {
                if (__usernameValue == null) {
                    throw new UnloadedException(User.class, "username");
                }
                return __usernameValue;
            }

            @Override
            @JsonIgnore
            public String password() {
                if (__passwordValue == null) {
                    throw new UnloadedException(User.class, "password");
                }
                return __passwordValue;
            }

            @Override
            @JsonIgnore
            public String name() {
                if (__nameValue == null) {
                    throw new UnloadedException(User.class, "name");
                }
                return __nameValue;
            }

            @Override
            @JsonIgnore
            public String email() {
                if (__emailValue == null) {
                    throw new UnloadedException(User.class, "email");
                }
                return __emailValue;
            }

            @Override
            @JsonIgnore
            public String sex() {
                if (__sexValue == null) {
                    throw new UnloadedException(User.class, "sex");
                }
                return __sexValue;
            }

            @Override
            @JsonIgnore
            public String nation() {
                if (__nationValue == null) {
                    throw new UnloadedException(User.class, "nation");
                }
                return __nationValue;
            }

            @Override
            @JsonIgnore
            public String birthplace() {
                if (__birthplaceValue == null) {
                    throw new UnloadedException(User.class, "birthplace");
                }
                return __birthplaceValue;
            }

            @Override
            @JsonIgnore
            public String birthdate() {
                if (__birthdateValue == null) {
                    throw new UnloadedException(User.class, "birthdate");
                }
                return __birthdateValue;
            }

            @Override
            @JsonIgnore
            public String idCard() {
                if (__idCardValue == null) {
                    throw new UnloadedException(User.class, "idCard");
                }
                return __idCardValue;
            }

            @Override
            @JsonIgnore
            public String mobile() {
                if (__mobileValue == null) {
                    throw new UnloadedException(User.class, "mobile");
                }
                return __mobileValue;
            }

            @Override
            @JsonIgnore
            public UserStateType state() {
                if (__stateValue == null) {
                    throw new UnloadedException(User.class, "state");
                }
                return __stateValue;
            }

            @Override
            @JsonIgnore
            public String orgId() {
                if (__orgIdValue == null) {
                    throw new UnloadedException(User.class, "orgId");
                }
                return __orgIdValue;
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_USERNAME:
                    		return __usernameValue != null;
                    case SLOT_PASSWORD:
                    		return __passwordValue != null;
                    case SLOT_NAME:
                    		return __nameValue != null;
                    case SLOT_EMAIL:
                    		return __emailValue != null;
                    case SLOT_SEX:
                    		return __sexValue != null;
                    case SLOT_NATION:
                    		return __nationValue != null;
                    case SLOT_BIRTHPLACE:
                    		return __birthplaceValue != null;
                    case SLOT_BIRTHDATE:
                    		return __birthdateValue != null;
                    case SLOT_ID_CARD:
                    		return __idCardValue != null;
                    case SLOT_MOBILE:
                    		return __mobileValue != null;
                    case SLOT_STATE:
                    		return __stateValue != null;
                    case SLOT_ORG_ID:
                    		return __orgIdValue != null;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.fast.basic.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "id":
                    		return __idLoaded;
                    case "username":
                    		return __usernameValue != null;
                    case "password":
                    		return __passwordValue != null;
                    case "name":
                    		return __nameValue != null;
                    case "email":
                    		return __emailValue != null;
                    case "sex":
                    		return __sexValue != null;
                    case "nation":
                    		return __nationValue != null;
                    case "birthplace":
                    		return __birthplaceValue != null;
                    case "birthdate":
                    		return __birthdateValue != null;
                    case "idCard":
                    		return __idCardValue != null;
                    case "mobile":
                    		return __mobileValue != null;
                    case "state":
                    		return __stateValue != null;
                    case "orgId":
                    		return __orgIdValue != null;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.fast.basic.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_USERNAME:
                    		return __visibility.visible(SLOT_USERNAME);
                    case SLOT_PASSWORD:
                    		return __visibility.visible(SLOT_PASSWORD);
                    case SLOT_NAME:
                    		return __visibility.visible(SLOT_NAME);
                    case SLOT_EMAIL:
                    		return __visibility.visible(SLOT_EMAIL);
                    case SLOT_SEX:
                    		return __visibility.visible(SLOT_SEX);
                    case SLOT_NATION:
                    		return __visibility.visible(SLOT_NATION);
                    case SLOT_BIRTHPLACE:
                    		return __visibility.visible(SLOT_BIRTHPLACE);
                    case SLOT_BIRTHDATE:
                    		return __visibility.visible(SLOT_BIRTHDATE);
                    case SLOT_ID_CARD:
                    		return __visibility.visible(SLOT_ID_CARD);
                    case SLOT_MOBILE:
                    		return __visibility.visible(SLOT_MOBILE);
                    case SLOT_STATE:
                    		return __visibility.visible(SLOT_STATE);
                    case SLOT_ORG_ID:
                    		return __visibility.visible(SLOT_ORG_ID);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "username":
                    		return __visibility.visible(SLOT_USERNAME);
                    case "password":
                    		return __visibility.visible(SLOT_PASSWORD);
                    case "name":
                    		return __visibility.visible(SLOT_NAME);
                    case "email":
                    		return __visibility.visible(SLOT_EMAIL);
                    case "sex":
                    		return __visibility.visible(SLOT_SEX);
                    case "nation":
                    		return __visibility.visible(SLOT_NATION);
                    case "birthplace":
                    		return __visibility.visible(SLOT_BIRTHPLACE);
                    case "birthdate":
                    		return __visibility.visible(SLOT_BIRTHDATE);
                    case "idCard":
                    		return __visibility.visible(SLOT_ID_CARD);
                    case "mobile":
                    		return __visibility.visible(SLOT_MOBILE);
                    case "state":
                    		return __visibility.visible(SLOT_STATE);
                    case "orgId":
                    		return __visibility.visible(SLOT_ORG_ID);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__usernameValue != null) {
                    hash = 31 * hash + __usernameValue.hashCode();
                }
                if (__passwordValue != null) {
                    hash = 31 * hash + __passwordValue.hashCode();
                }
                if (__nameValue != null) {
                    hash = 31 * hash + __nameValue.hashCode();
                }
                if (__emailValue != null) {
                    hash = 31 * hash + __emailValue.hashCode();
                }
                if (__sexValue != null) {
                    hash = 31 * hash + __sexValue.hashCode();
                }
                if (__nationValue != null) {
                    hash = 31 * hash + __nationValue.hashCode();
                }
                if (__birthplaceValue != null) {
                    hash = 31 * hash + __birthplaceValue.hashCode();
                }
                if (__birthdateValue != null) {
                    hash = 31 * hash + __birthdateValue.hashCode();
                }
                if (__idCardValue != null) {
                    hash = 31 * hash + __idCardValue.hashCode();
                }
                if (__mobileValue != null) {
                    hash = 31 * hash + __mobileValue.hashCode();
                }
                if (__stateValue != null) {
                    hash = 31 * hash + __stateValue.hashCode();
                }
                if (__orgIdValue != null) {
                    hash = 31 * hash + __orgIdValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__usernameValue != null) {
                    hash = 31 * hash + System.identityHashCode(__usernameValue);
                }
                if (__passwordValue != null) {
                    hash = 31 * hash + System.identityHashCode(__passwordValue);
                }
                if (__nameValue != null) {
                    hash = 31 * hash + System.identityHashCode(__nameValue);
                }
                if (__emailValue != null) {
                    hash = 31 * hash + System.identityHashCode(__emailValue);
                }
                if (__sexValue != null) {
                    hash = 31 * hash + System.identityHashCode(__sexValue);
                }
                if (__nationValue != null) {
                    hash = 31 * hash + System.identityHashCode(__nationValue);
                }
                if (__birthplaceValue != null) {
                    hash = 31 * hash + System.identityHashCode(__birthplaceValue);
                }
                if (__birthdateValue != null) {
                    hash = 31 * hash + System.identityHashCode(__birthdateValue);
                }
                if (__idCardValue != null) {
                    hash = 31 * hash + System.identityHashCode(__idCardValue);
                }
                if (__mobileValue != null) {
                    hash = 31 * hash + System.identityHashCode(__mobileValue);
                }
                if (__stateValue != null) {
                    hash = 31 * hash + System.identityHashCode(__stateValue);
                }
                if (__orgIdValue != null) {
                    hash = 31 * hash + System.identityHashCode(__orgIdValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_USERNAME)) != __other.__isVisible(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                boolean __usernameLoaded = __usernameValue != null;
                if (__usernameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                if (__usernameLoaded && !Objects.equals(__usernameValue, __other.username())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_PASSWORD)) != __other.__isVisible(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                boolean __passwordLoaded = __passwordValue != null;
                if (__passwordLoaded != __other.__isLoaded(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                if (__passwordLoaded && !Objects.equals(__passwordValue, __other.password())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                boolean __nameLoaded = __nameValue != null;
                if (__nameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                if (__nameLoaded && !Objects.equals(__nameValue, __other.name())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EMAIL)) != __other.__isVisible(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                boolean __emailLoaded = __emailValue != null;
                if (__emailLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                if (__emailLoaded && !Objects.equals(__emailValue, __other.email())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SEX)) != __other.__isVisible(PropId.byIndex(SLOT_SEX))) {
                    return false;
                }
                boolean __sexLoaded = __sexValue != null;
                if (__sexLoaded != __other.__isLoaded(PropId.byIndex(SLOT_SEX))) {
                    return false;
                }
                if (__sexLoaded && !Objects.equals(__sexValue, __other.sex())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_NATION)) != __other.__isVisible(PropId.byIndex(SLOT_NATION))) {
                    return false;
                }
                boolean __nationLoaded = __nationValue != null;
                if (__nationLoaded != __other.__isLoaded(PropId.byIndex(SLOT_NATION))) {
                    return false;
                }
                if (__nationLoaded && !Objects.equals(__nationValue, __other.nation())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BIRTHPLACE)) != __other.__isVisible(PropId.byIndex(SLOT_BIRTHPLACE))) {
                    return false;
                }
                boolean __birthplaceLoaded = __birthplaceValue != null;
                if (__birthplaceLoaded != __other.__isLoaded(PropId.byIndex(SLOT_BIRTHPLACE))) {
                    return false;
                }
                if (__birthplaceLoaded && !Objects.equals(__birthplaceValue, __other.birthplace())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BIRTHDATE)) != __other.__isVisible(PropId.byIndex(SLOT_BIRTHDATE))) {
                    return false;
                }
                boolean __birthdateLoaded = __birthdateValue != null;
                if (__birthdateLoaded != __other.__isLoaded(PropId.byIndex(SLOT_BIRTHDATE))) {
                    return false;
                }
                if (__birthdateLoaded && !Objects.equals(__birthdateValue, __other.birthdate())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ID_CARD)) != __other.__isVisible(PropId.byIndex(SLOT_ID_CARD))) {
                    return false;
                }
                boolean __idCardLoaded = __idCardValue != null;
                if (__idCardLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID_CARD))) {
                    return false;
                }
                if (__idCardLoaded && !Objects.equals(__idCardValue, __other.idCard())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MOBILE)) != __other.__isVisible(PropId.byIndex(SLOT_MOBILE))) {
                    return false;
                }
                boolean __mobileLoaded = __mobileValue != null;
                if (__mobileLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MOBILE))) {
                    return false;
                }
                if (__mobileLoaded && !Objects.equals(__mobileValue, __other.mobile())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_STATE)) != __other.__isVisible(PropId.byIndex(SLOT_STATE))) {
                    return false;
                }
                boolean __stateLoaded = __stateValue != null;
                if (__stateLoaded != __other.__isLoaded(PropId.byIndex(SLOT_STATE))) {
                    return false;
                }
                if (__stateLoaded && !Objects.equals(__stateValue, __other.state())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false;
                }
                boolean __orgIdLoaded = __orgIdValue != null;
                if (__orgIdLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ORG_ID))) {
                    return false;
                }
                if (__orgIdLoaded && !Objects.equals(__orgIdValue, __other.orgId())) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USERNAME)) != __other.__isVisible(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                boolean __usernameLoaded = __usernameValue != null;
                if (__usernameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                if (__usernameLoaded && __usernameValue != __other.username()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_PASSWORD)) != __other.__isVisible(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                boolean __passwordLoaded = __passwordValue != null;
                if (__passwordLoaded != __other.__isLoaded(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                if (__passwordLoaded && __passwordValue != __other.password()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                boolean __nameLoaded = __nameValue != null;
                if (__nameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                if (__nameLoaded && __nameValue != __other.name()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EMAIL)) != __other.__isVisible(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                boolean __emailLoaded = __emailValue != null;
                if (__emailLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                if (__emailLoaded && __emailValue != __other.email()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SEX)) != __other.__isVisible(PropId.byIndex(SLOT_SEX))) {
                    return false;
                }
                boolean __sexLoaded = __sexValue != null;
                if (__sexLoaded != __other.__isLoaded(PropId.byIndex(SLOT_SEX))) {
                    return false;
                }
                if (__sexLoaded && __sexValue != __other.sex()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_NATION)) != __other.__isVisible(PropId.byIndex(SLOT_NATION))) {
                    return false;
                }
                boolean __nationLoaded = __nationValue != null;
                if (__nationLoaded != __other.__isLoaded(PropId.byIndex(SLOT_NATION))) {
                    return false;
                }
                if (__nationLoaded && __nationValue != __other.nation()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BIRTHPLACE)) != __other.__isVisible(PropId.byIndex(SLOT_BIRTHPLACE))) {
                    return false;
                }
                boolean __birthplaceLoaded = __birthplaceValue != null;
                if (__birthplaceLoaded != __other.__isLoaded(PropId.byIndex(SLOT_BIRTHPLACE))) {
                    return false;
                }
                if (__birthplaceLoaded && __birthplaceValue != __other.birthplace()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BIRTHDATE)) != __other.__isVisible(PropId.byIndex(SLOT_BIRTHDATE))) {
                    return false;
                }
                boolean __birthdateLoaded = __birthdateValue != null;
                if (__birthdateLoaded != __other.__isLoaded(PropId.byIndex(SLOT_BIRTHDATE))) {
                    return false;
                }
                if (__birthdateLoaded && __birthdateValue != __other.birthdate()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ID_CARD)) != __other.__isVisible(PropId.byIndex(SLOT_ID_CARD))) {
                    return false;
                }
                boolean __idCardLoaded = __idCardValue != null;
                if (__idCardLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID_CARD))) {
                    return false;
                }
                if (__idCardLoaded && __idCardValue != __other.idCard()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MOBILE)) != __other.__isVisible(PropId.byIndex(SLOT_MOBILE))) {
                    return false;
                }
                boolean __mobileLoaded = __mobileValue != null;
                if (__mobileLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MOBILE))) {
                    return false;
                }
                if (__mobileLoaded && __mobileValue != __other.mobile()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_STATE)) != __other.__isVisible(PropId.byIndex(SLOT_STATE))) {
                    return false;
                }
                boolean __stateLoaded = __stateValue != null;
                if (__stateLoaded != __other.__isLoaded(PropId.byIndex(SLOT_STATE))) {
                    return false;
                }
                if (__stateLoaded && __stateValue != __other.state()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false;
                }
                boolean __orgIdLoaded = __orgIdValue != null;
                if (__orgIdLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ORG_ID))) {
                    return false;
                }
                if (__orgIdLoaded && __orgIdValue != __other.orgId()) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = User.class
        )
        private static class DraftImpl extends Implementor implements DraftSpi, UserDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private User __resolved;

            DraftImpl(DraftContext ctx, User base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public UserDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String username() {
                return (__modified!= null ? __modified : __base).username();
            }

            @Override
            public UserDraft setUsername(String username) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (username == null) {
                    throw new IllegalArgumentException(
                        "'username' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__usernameValue = username;
                return this;
            }

            @Override
            @JsonIgnore
            public String password() {
                return (__modified!= null ? __modified : __base).password();
            }

            @Override
            public UserDraft setPassword(String password) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (password == null) {
                    throw new IllegalArgumentException(
                        "'password' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__passwordValue = password;
                return this;
            }

            @Override
            @JsonIgnore
            public String name() {
                return (__modified!= null ? __modified : __base).name();
            }

            @Override
            public UserDraft setName(String name) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (name == null) {
                    throw new IllegalArgumentException(
                        "'name' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__nameValue = name;
                return this;
            }

            @Override
            @JsonIgnore
            public String email() {
                return (__modified!= null ? __modified : __base).email();
            }

            @Override
            public UserDraft setEmail(String email) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (email == null) {
                    throw new IllegalArgumentException(
                        "'email' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__emailValue = email;
                return this;
            }

            @Override
            @JsonIgnore
            public String sex() {
                return (__modified!= null ? __modified : __base).sex();
            }

            @Override
            public UserDraft setSex(String sex) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (sex == null) {
                    throw new IllegalArgumentException(
                        "'sex' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__sexValue = sex;
                return this;
            }

            @Override
            @JsonIgnore
            public String nation() {
                return (__modified!= null ? __modified : __base).nation();
            }

            @Override
            public UserDraft setNation(String nation) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (nation == null) {
                    throw new IllegalArgumentException(
                        "'nation' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__nationValue = nation;
                return this;
            }

            @Override
            @JsonIgnore
            public String birthplace() {
                return (__modified!= null ? __modified : __base).birthplace();
            }

            @Override
            public UserDraft setBirthplace(String birthplace) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (birthplace == null) {
                    throw new IllegalArgumentException(
                        "'birthplace' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__birthplaceValue = birthplace;
                return this;
            }

            @Override
            @JsonIgnore
            public String birthdate() {
                return (__modified!= null ? __modified : __base).birthdate();
            }

            @Override
            public UserDraft setBirthdate(String birthdate) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (birthdate == null) {
                    throw new IllegalArgumentException(
                        "'birthdate' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__birthdateValue = birthdate;
                return this;
            }

            @Override
            @JsonIgnore
            public String idCard() {
                return (__modified!= null ? __modified : __base).idCard();
            }

            @Override
            public UserDraft setIdCard(String idCard) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (idCard == null) {
                    throw new IllegalArgumentException(
                        "'idCard' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idCardValue = idCard;
                return this;
            }

            @Override
            @JsonIgnore
            public String mobile() {
                return (__modified!= null ? __modified : __base).mobile();
            }

            @Override
            public UserDraft setMobile(String mobile) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (mobile == null) {
                    throw new IllegalArgumentException(
                        "'mobile' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__mobileValue = mobile;
                return this;
            }

            @Override
            @JsonIgnore
            public UserStateType state() {
                return (__modified!= null ? __modified : __base).state();
            }

            @Override
            public UserDraft setState(UserStateType state) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (state == null) {
                    throw new IllegalArgumentException(
                        "'state' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__stateValue = state;
                return this;
            }

            @Override
            @JsonIgnore
            public String orgId() {
                return (__modified!= null ? __modified : __base).orgId();
            }

            @Override
            public UserDraft setOrgId(String orgId) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (orgId == null) {
                    throw new IllegalArgumentException(
                        "'orgId' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__orgIdValue = orgId;
                return this;
            }

            @SuppressWarnings("all")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_USERNAME:
                    		setUsername((String)value);break;
                    case SLOT_PASSWORD:
                    		setPassword((String)value);break;
                    case SLOT_NAME:
                    		setName((String)value);break;
                    case SLOT_EMAIL:
                    		setEmail((String)value);break;
                    case SLOT_SEX:
                    		setSex((String)value);break;
                    case SLOT_NATION:
                    		setNation((String)value);break;
                    case SLOT_BIRTHPLACE:
                    		setBirthplace((String)value);break;
                    case SLOT_BIRTHDATE:
                    		setBirthdate((String)value);break;
                    case SLOT_ID_CARD:
                    		setIdCard((String)value);break;
                    case SLOT_MOBILE:
                    		setMobile((String)value);break;
                    case SLOT_STATE:
                    		setState((UserStateType)value);break;
                    case SLOT_ORG_ID:
                    		setOrgId((String)value);break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.fast.basic.entity.User\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("all")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "username":
                    		setUsername((String)value);break;
                    case "password":
                    		setPassword((String)value);break;
                    case "name":
                    		setName((String)value);break;
                    case "email":
                    		setEmail((String)value);break;
                    case "sex":
                    		setSex((String)value);break;
                    case "nation":
                    		setNation((String)value);break;
                    case "birthplace":
                    		setBirthplace((String)value);break;
                    case "birthdate":
                    		setBirthdate((String)value);break;
                    case "idCard":
                    		setIdCard((String)value);break;
                    case "mobile":
                    		setMobile((String)value);break;
                    case "state":
                    		setState((UserStateType)value);break;
                    case "orgId":
                    		setOrgId((String)value);break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.fast.basic.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(13);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_USERNAME:
                    		__visibility.show(SLOT_USERNAME, visible);break;
                    case SLOT_PASSWORD:
                    		__visibility.show(SLOT_PASSWORD, visible);break;
                    case SLOT_NAME:
                    		__visibility.show(SLOT_NAME, visible);break;
                    case SLOT_EMAIL:
                    		__visibility.show(SLOT_EMAIL, visible);break;
                    case SLOT_SEX:
                    		__visibility.show(SLOT_SEX, visible);break;
                    case SLOT_NATION:
                    		__visibility.show(SLOT_NATION, visible);break;
                    case SLOT_BIRTHPLACE:
                    		__visibility.show(SLOT_BIRTHPLACE, visible);break;
                    case SLOT_BIRTHDATE:
                    		__visibility.show(SLOT_BIRTHDATE, visible);break;
                    case SLOT_ID_CARD:
                    		__visibility.show(SLOT_ID_CARD, visible);break;
                    case SLOT_MOBILE:
                    		__visibility.show(SLOT_MOBILE, visible);break;
                    case SLOT_STATE:
                    		__visibility.show(SLOT_STATE, visible);break;
                    case SLOT_ORG_ID:
                    		__visibility.show(SLOT_ORG_ID, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.fast.basic.entity.User\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(13);
                }
                switch (prop) {
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "username":
                    		__visibility.show(SLOT_USERNAME, visible);break;
                    case "password":
                    		__visibility.show(SLOT_PASSWORD, visible);break;
                    case "name":
                    		__visibility.show(SLOT_NAME, visible);break;
                    case "email":
                    		__visibility.show(SLOT_EMAIL, visible);break;
                    case "sex":
                    		__visibility.show(SLOT_SEX, visible);break;
                    case "nation":
                    		__visibility.show(SLOT_NATION, visible);break;
                    case "birthplace":
                    		__visibility.show(SLOT_BIRTHPLACE, visible);break;
                    case "birthdate":
                    		__visibility.show(SLOT_BIRTHDATE, visible);break;
                    case "idCard":
                    		__visibility.show(SLOT_ID_CARD, visible);break;
                    case "mobile":
                    		__visibility.show(SLOT_MOBILE, visible);break;
                    case "state":
                    		__visibility.show(SLOT_STATE, visible);break;
                    case "orgId":
                    		__visibility.show(SLOT_ORG_ID, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.fast.basic.entity.User\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_ID:
                    		__modified().__idValue = 0;
                    __modified().__idLoaded = false;break;
                    case SLOT_USERNAME:
                    		__modified().__usernameValue = null;break;
                    case SLOT_PASSWORD:
                    		__modified().__passwordValue = null;break;
                    case SLOT_NAME:
                    		__modified().__nameValue = null;break;
                    case SLOT_EMAIL:
                    		__modified().__emailValue = null;break;
                    case SLOT_SEX:
                    		__modified().__sexValue = null;break;
                    case SLOT_NATION:
                    		__modified().__nationValue = null;break;
                    case SLOT_BIRTHPLACE:
                    		__modified().__birthplaceValue = null;break;
                    case SLOT_BIRTHDATE:
                    		__modified().__birthdateValue = null;break;
                    case SLOT_ID_CARD:
                    		__modified().__idCardValue = null;break;
                    case SLOT_MOBILE:
                    		__modified().__mobileValue = null;break;
                    case SLOT_STATE:
                    		__modified().__stateValue = null;break;
                    case SLOT_ORG_ID:
                    		__modified().__orgIdValue = null;break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.fast.basic.entity.User\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "id":
                    		__modified().__idValue = 0;
                    __modified().__idLoaded = false;break;
                    case "username":
                    		__modified().__usernameValue = null;break;
                    case "password":
                    		__modified().__passwordValue = null;break;
                    case "name":
                    		__modified().__nameValue = null;break;
                    case "email":
                    		__modified().__emailValue = null;break;
                    case "sex":
                    		__modified().__sexValue = null;break;
                    case "nation":
                    		__modified().__nationValue = null;break;
                    case "birthplace":
                    		__modified().__birthplaceValue = null;break;
                    case "birthdate":
                    		__modified().__birthdateValue = null;break;
                    case "idCard":
                    		__modified().__idCardValue = null;break;
                    case "mobile":
                    		__modified().__mobileValue = null;break;
                    case "state":
                    		__modified().__stateValue = null;break;
                    case "orgId":
                    		__modified().__orgIdValue = null;break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.fast.basic.entity.User\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = User.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            this(null);
        }

        public Builder(@Nullable User base) {
            __draft = new Producer.DraftImpl(null, base);
        }

        public Builder id(@NotNull Long id) {
            if (id != null) {
                __draft.setId(id);
            }
            return this;
        }

        public Builder username(@NotNull String username) {
            if (username != null) {
                __draft.setUsername(username);
            }
            return this;
        }

        public Builder password(@NotNull String password) {
            if (password != null) {
                __draft.setPassword(password);
            }
            return this;
        }

        public Builder name(@NotNull String name) {
            if (name != null) {
                __draft.setName(name);
            }
            return this;
        }

        public Builder email(@NotNull String email) {
            if (email != null) {
                __draft.setEmail(email);
            }
            return this;
        }

        public Builder sex(@NotNull String sex) {
            if (sex != null) {
                __draft.setSex(sex);
            }
            return this;
        }

        public Builder nation(@NotNull String nation) {
            if (nation != null) {
                __draft.setNation(nation);
            }
            return this;
        }

        public Builder birthplace(@NotNull String birthplace) {
            if (birthplace != null) {
                __draft.setBirthplace(birthplace);
            }
            return this;
        }

        public Builder birthdate(@NotNull String birthdate) {
            if (birthdate != null) {
                __draft.setBirthdate(birthdate);
            }
            return this;
        }

        public Builder idCard(@NotNull String idCard) {
            if (idCard != null) {
                __draft.setIdCard(idCard);
            }
            return this;
        }

        public Builder mobile(@NotNull String mobile) {
            if (mobile != null) {
                __draft.setMobile(mobile);
            }
            return this;
        }

        public Builder state(@NotNull UserStateType state) {
            if (state != null) {
                __draft.setState(state);
            }
            return this;
        }

        public Builder orgId(@NotNull String orgId) {
            if (orgId != null) {
                __draft.setOrgId(orgId);
            }
            return this;
        }

        public User build() {
            return (User)__draft.__modified();
        }
    }
}
