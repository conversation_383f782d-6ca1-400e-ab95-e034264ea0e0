package com.fast.basic.entity;

import java.lang.Override;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = User.class
)
public class UserFetcher extends AbstractTypedFetcher<User, UserFetcher> {
    public static final UserFetcher $ = new UserFetcher(null);

    private UserFetcher(FetcherImpl<User> base) {
        super(User.class, base);
    }

    private UserFetcher(UserFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private UserFetcher(UserFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static UserFetcher $from(Fetcher<User> base) {
        return base instanceof UserFetcher ? 
        	(UserFetcher)base : 
        	new UserFetcher((FetcherImpl<User>)base);
    }

    @NewChain
    public UserFetcher username() {
        return add("username");
    }

    @NewChain
    public UserFetcher username(boolean enabled) {
        return enabled ? add("username") : remove("username");
    }

    @NewChain
    public UserFetcher password() {
        return add("password");
    }

    @NewChain
    public UserFetcher password(boolean enabled) {
        return enabled ? add("password") : remove("password");
    }

    @NewChain
    public UserFetcher name() {
        return add("name");
    }

    @NewChain
    public UserFetcher name(boolean enabled) {
        return enabled ? add("name") : remove("name");
    }

    @NewChain
    public UserFetcher email() {
        return add("email");
    }

    @NewChain
    public UserFetcher email(boolean enabled) {
        return enabled ? add("email") : remove("email");
    }

    @NewChain
    public UserFetcher sex() {
        return add("sex");
    }

    @NewChain
    public UserFetcher sex(boolean enabled) {
        return enabled ? add("sex") : remove("sex");
    }

    @NewChain
    public UserFetcher nation() {
        return add("nation");
    }

    @NewChain
    public UserFetcher nation(boolean enabled) {
        return enabled ? add("nation") : remove("nation");
    }

    @NewChain
    public UserFetcher birthplace() {
        return add("birthplace");
    }

    @NewChain
    public UserFetcher birthplace(boolean enabled) {
        return enabled ? add("birthplace") : remove("birthplace");
    }

    @NewChain
    public UserFetcher birthdate() {
        return add("birthdate");
    }

    @NewChain
    public UserFetcher birthdate(boolean enabled) {
        return enabled ? add("birthdate") : remove("birthdate");
    }

    @NewChain
    public UserFetcher idCard() {
        return add("idCard");
    }

    @NewChain
    public UserFetcher idCard(boolean enabled) {
        return enabled ? add("idCard") : remove("idCard");
    }

    @NewChain
    public UserFetcher mobile() {
        return add("mobile");
    }

    @NewChain
    public UserFetcher mobile(boolean enabled) {
        return enabled ? add("mobile") : remove("mobile");
    }

    @NewChain
    public UserFetcher state() {
        return add("state");
    }

    @NewChain
    public UserFetcher state(boolean enabled) {
        return enabled ? add("state") : remove("state");
    }

    @NewChain
    public UserFetcher orgId() {
        return add("orgId");
    }

    @NewChain
    public UserFetcher orgId(boolean enabled) {
        return enabled ? add("orgId") : remove("orgId");
    }

    @Override
    protected UserFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new UserFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected UserFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new UserFetcher(this, prop, fieldConfig);
    }
}
