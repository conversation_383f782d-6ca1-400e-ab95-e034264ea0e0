package com.fast.basic.entity;

import com.fast.basic.entity.type.UserStateType;
import java.lang.Long;
import java.lang.String;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.Props;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = User.class
)
@PropsFor(User.class)
public interface UserProps extends Props {
    TypedProp.Scalar<User, Long> ID = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("id"));

    TypedProp.Scalar<User, String> USERNAME = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("username"));

    TypedProp.Scalar<User, String> PASSWORD = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("password"));

    TypedProp.Scalar<User, String> NAME = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("name"));

    TypedProp.Scalar<User, String> EMAIL = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("email"));

    TypedProp.Scalar<User, String> SEX = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("sex"));

    TypedProp.Scalar<User, String> NATION = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("nation"));

    TypedProp.Scalar<User, String> BIRTHPLACE = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("birthplace"));

    TypedProp.Scalar<User, String> BIRTHDATE = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("birthdate"));

    TypedProp.Scalar<User, String> ID_CARD = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("idCard"));

    TypedProp.Scalar<User, String> MOBILE = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("mobile"));

    TypedProp.Scalar<User, UserStateType> STATE = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("state"));

    TypedProp.Scalar<User, String> ORG_ID = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("orgId"));

    PropExpression.Num<Long> id();

    PropExpression.Str username();

    PropExpression.Str password();

    PropExpression.Str name();

    PropExpression.Str email();

    PropExpression.Str sex();

    PropExpression.Str nation();

    PropExpression.Str birthplace();

    PropExpression.Str birthdate();

    PropExpression.Str idCard();

    PropExpression.Str mobile();

    PropExpression.Cmp<UserStateType> state();

    PropExpression.Str orgId();
}
