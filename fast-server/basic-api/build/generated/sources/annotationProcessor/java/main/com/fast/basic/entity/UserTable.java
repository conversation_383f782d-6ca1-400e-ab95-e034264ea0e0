package com.fast.basic.entity;

import com.fast.basic.entity.type.UserStateType;
import java.lang.Deprecated;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = User.class
)
public class UserTable extends AbstractTypedTable<User> implements UserProps {
    public static final UserTable $ = new UserTable();

    public UserTable() {
        super(User.class);
    }

    public UserTable(AbstractTypedTable.DelayedOperation<User> delayedOperation) {
        super(User.class, delayedOperation);
    }

    public UserTable(TableImplementor<User> table) {
        super(table);
    }

    protected UserTable(UserTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Num<Long> id() {
        return __get(UserProps.ID.unwrap());
    }

    @Override
    public PropExpression.Str username() {
        return __get(UserProps.USERNAME.unwrap());
    }

    @Override
    public PropExpression.Str password() {
        return __get(UserProps.PASSWORD.unwrap());
    }

    @Override
    public PropExpression.Str name() {
        return __get(UserProps.NAME.unwrap());
    }

    @Override
    public PropExpression.Str email() {
        return __get(UserProps.EMAIL.unwrap());
    }

    @Override
    public PropExpression.Str sex() {
        return __get(UserProps.SEX.unwrap());
    }

    @Override
    public PropExpression.Str nation() {
        return __get(UserProps.NATION.unwrap());
    }

    @Override
    public PropExpression.Str birthplace() {
        return __get(UserProps.BIRTHPLACE.unwrap());
    }

    @Override
    public PropExpression.Str birthdate() {
        return __get(UserProps.BIRTHDATE.unwrap());
    }

    @Override
    public PropExpression.Str idCard() {
        return __get(UserProps.ID_CARD.unwrap());
    }

    @Override
    public PropExpression.Str mobile() {
        return __get(UserProps.MOBILE.unwrap());
    }

    @Override
    public PropExpression.Cmp<UserStateType> state() {
        return __get(UserProps.STATE.unwrap());
    }

    @Override
    public PropExpression.Str orgId() {
        return __get(UserProps.ORG_ID.unwrap());
    }

    @Override
    public UserTableEx asTableEx() {
        return new UserTableEx(this, null);
    }

    @Override
    public UserTable __disableJoin(String reason) {
        return new UserTable(this, reason);
    }

    @GeneratedBy(
            type = User.class
    )
    public static class Remote extends AbstractTypedTable<User> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(User.class, delayedOperation);
        }

        public Remote(TableImplementor<User> table) {
            super(table);
        }

        public PropExpression.Num<Long> id() {
            return __get(UserProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<User> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
