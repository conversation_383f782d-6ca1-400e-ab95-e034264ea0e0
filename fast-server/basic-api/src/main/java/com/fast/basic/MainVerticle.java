package com.fast.basic;

import com.fast.basic.entity.Fetchers;
import com.fast.basic.entity.User;
import com.fast.basic.entity.UserFetcher;
import com.fast.basic.entity.UserTable;
import io.vertx.core.Future;
import io.vertx.core.VerticleBase;
import io.vertx.ext.web.Router;
import io.vertx.jdbcclient.JDBCPool;
import io.vertx.pgclient.PgBuilder;
import io.vertx.sqlclient.Pool;
import io.vertx.sqlclient.SqlClient;
import org.babyfish.jimmer.sql.JSqlClient;
import org.babyfish.jimmer.sql.dialect.PostgresDialect;
import org.babyfish.jimmer.sql.runtime.ConnectionManager;
import org.babyfish.jimmer.sql.runtime.DatabaseValidationMode;
import org.babyfish.jimmer.sql.runtime.Executor;
import org.babyfish.jimmer.sql.runtime.SqlFormatter;
import org.babyfish.jimmer.sql.transaction.AbstractTxConnectionManager;
import org.jetbrains.annotations.Nullable;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;


public class MainVerticle extends VerticleBase {

  private static final UserTable USER_TABLE = UserTable.$;


  @Override
  public Future<?> start() {
    return vertx.createHttpServer()
      .requestHandler(req -> {
        Pool pool = PgBuilder.pool().build();
        pool.getConnection().onSuccess(conn -> {
        JSqlClient jSqlClient = createJSqlClient();
        List<User> users = jSqlClient.createQuery(USER_TABLE)
          .where(USER_TABLE.name().ne("John"))
          .select(USER_TABLE.fetch(
            Fetchers.USER_FETCHER.allScalarFields()
          ))
          .execute();
        req.response()
          .putHeader("content-type", "text/plain")
          .end("Hello from Vert.x! " + users.size());
          conn.close();
        });


      }).listen(9000).onSuccess(http -> {
        System.out.println("HTTP server started on port 9000");
      });
  }

  public JSqlClient createJSqlClient() {
    return JSqlClient.newBuilder()
      .setDialect(new PostgresDialect())
      .setExecutor(Executor.log())
      .setSqlFormatter(SqlFormatter.INLINE_PRETTY)
      .setExecutorContextPrefixes(
        Collections.singleton(
          "com.fast.basic"
        )
      )
      .setDatabaseValidationMode(
        DatabaseValidationMode.ERROR
      )
      .setInListPaddingEnabled(true)
      .build();
  }

}
