package com.fast.basic.entity;
import com.fast.basic.entity.type.UserStateType;
import org.babyfish.jimmer.sql.*;
import org.jetbrains.annotations.Nullable;

@Entity
public interface User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();


    @Key
    String username();

    @Key
    String password();

    @Key
    String name();

    @Key
    String email();

    @Key
    String sex();

    @Key
    String nation();

    @Key
    String birthplace();

    @Key
    String birthdate();

    @Key
    String idCard();

    @Key
    String mobile();

    UserStateType state();

    @Key
    String orgId();

}
