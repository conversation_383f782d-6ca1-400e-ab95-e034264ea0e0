plugins {
    java
    `java-library`
}

group = "com.fast.core"


val vertxVersion = "5.0.1"
val junitJupiterVersion = "5.9.1"
val lombokVersion = "1.18.34"
var validatorVersion = "3.1.1"
var persistenceVersion = "3.2.0"
val commonsLangVersion = "3.18.0"
val commonsCodecVersion = "1.19.0"


dependencies {
    implementation(platform("io.vertx:vertx-stack-depchain:$vertxVersion"))
    implementation("io.vertx:vertx-launcher-application")
    implementation("io.vertx:vertx-web-validation")
    implementation("io.vertx:vertx-web")
    implementation("io.vertx:vertx-pg-client")
    implementation("com.ongres.scram:client:2.1")

    implementation(project(":fast-server:tools"))
    implementation("org.apache.commons:commons-lang3:$commonsLangVersion")
    implementation("commons-codec:commons-codec:$commonsCodecVersion")

    implementation("jakarta.validation:jakarta.validation-api:$validatorVersion")
    implementation("jakarta.persistence:jakarta.persistence-api:${persistenceVersion}")


    compileOnly("org.projectlombok:lombok:$lombokVersion")
    annotationProcessor("org.projectlombok:lombok:$lombokVersion")
    testCompileOnly("org.projectlombok:lombok:$lombokVersion")
    testAnnotationProcessor("org.projectlombok:lombok:$lombokVersion")

    testImplementation("io.vertx:vertx-junit5")
    testImplementation("org.junit.jupiter:junit-jupiter:$junitJupiterVersion")
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}
