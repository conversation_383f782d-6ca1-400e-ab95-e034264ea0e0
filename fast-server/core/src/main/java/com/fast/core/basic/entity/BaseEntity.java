package com.fast.core.basic.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@ToString
@MappedSuperclass
public abstract class BaseEntity implements Entity, UserOperation, TimeOperation {

    private static final long serialVersionUID = -8040949919319184095L;


    @Id
    @GeneratedValue(generator = "uuid")
    @Column(name = "id", length = 50, updatable = false,comment = "主键" )
    protected String id;

    @Column(name = "create_user_id", nullable = false, length = 50,comment = "创建人主键")
    protected String createUserId;

    @Column(name = "creator", nullable = false, length = 400,comment = "创建人")
    protected String creator;

    @Column(name = "create_date", nullable = false,comment = "创建时间")
    protected LocalDateTime createDate;

    @Column(name = "modify_user_id", nullable = false, length = 50,comment = "修改人主键")
    protected String modifyUserId;

    @Column(name = "modifier", nullable = false, length = 400,comment = "修改人")
    protected String modifier;

    @Column(name = "modify_date", nullable = false,comment = "修改时间")
    protected LocalDateTime modifyDate;

    @Column(name = "belong_id", nullable = false, length = 50,comment ="归属id")
    protected String belongId = "0";


    @PrePersist
    protected void onCreate() {
//        if (StringUtils.isBlank(createUserId)) {
//            createUserId = USER_SESSION.getUserId().orElse("developer");
//        }
//        if (StringUtils.isEmpty(creator)) {
//            creator = USER_SESSION.getUsername().orElse("developer");
//        }
        if (createDate == null) {
            createDate = LocalDateTime.now();
        }
        update();
//        if (StringUtils.isBlank(belongId)) {
//            belongId = USER_SESSION.getBelongId().orElse("developer");
//        }
    }

    @PreUpdate
    protected void onUpdate() {
        update();
    }

    private void update() {
//        modifier = USER_SESSION.getUsername().orElse("developer");
//        modifyUserId = USER_SESSION.getUserId().orElse("developer");
        modifyDate = LocalDateTime.now();
    }

}
