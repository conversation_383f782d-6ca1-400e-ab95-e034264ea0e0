package com.fast.core.basic.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

/**
 * @program: project-management-git
 * @description
 * @author: liunan
 * @create: 2022/3/7 22:35
 **/
@Getter
@Setter
@NoArgsConstructor
@ToString
@MappedSuperclass
public abstract class BaseVersionEntity extends BaseEntity implements VersionOperation {


    @Version
    @Column(name = "version",comment = "版本号")
    private Integer version;


}
