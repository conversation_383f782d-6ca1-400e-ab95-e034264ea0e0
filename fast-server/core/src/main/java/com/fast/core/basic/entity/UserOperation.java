package com.fast.core.basic.entity;

public interface UserOperation {

    public String getCreateUserId();

    public void setCreateUserId(String createUserId);

    public String getCreator();

    public void setCreator(String creator);

    public String getModifyUserId();

    public void setModifyUserId(String modifyUserId);

    public String getModifier();

    public void setModifier(String modifier);

}
