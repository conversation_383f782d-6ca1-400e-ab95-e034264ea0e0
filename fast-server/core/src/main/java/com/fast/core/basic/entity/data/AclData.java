package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.data.type.AclType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

@Entity
@Table(name = "sys_data_acl",comment = "数据安全策略表")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class AclData extends BaseEntity {

    private static final long serialVersionUID = -6554538449029163631L;

    @Column(name = "acl_table_name", nullable = false, length = 50,comment = "策略应用的表")
    private String aclTableName;

    @Column(name = "acl_data_id", nullable = false, length = 50,comment = "策略对应的单条数据")
    private String aclDataId;

    @Column(name = "acl_cmd", nullable = false, precision = 0,comment = "策略应用的命令类型")
    private Integer aclCmd;

    @Column(name = "acl_role", nullable = false, length = 50,comment = "角色编码")
    private String aclRole;

    @Column(name = "acl_type", nullable = false,comment = "ACL类型")
    @Enumerated(EnumType.STRING)
    private AclType aclType = AclType.AUTH;

    @Column(name = "acl_allow", nullable = false,comment = "是否允许")
    private Boolean allow = true;
}
