package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "sys_auth",comment = "权限标示符表")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class Auth extends BaseEntity {
    private static final long serialVersionUID = -6438459034480956584L;

    @Column(name = "rol_code", unique = true, nullable = false, length = 400,comment = "权限编码")
    private String rolCode;

    @Column(name = "rol_name", nullable = false, length = 400,comment = "权限名称")
    private String rolName;

    @Column(name = "description", length = 2000,comment = "权限说明")
    private String description;

    @Column(name = "rol_super", nullable = false,comment = "是否超级权限")
    private Boolean rolSuper = false;

    @Column(name = "rol_inherit", nullable = false,comment = "是否继承")
    private Boolean rolInherit = false;

    @Column(name = "rol_create_role", nullable = false,comment = "是否能创建角色")
    private Boolean rolCreateRole = false;

//    @OneToMany
//    @JoinColumn(name = "pol_role")
//    private Set<Policy> policies;

}
