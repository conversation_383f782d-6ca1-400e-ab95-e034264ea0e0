package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

@Entity
@Table(name = "sys_auth_members", comment = "权限标示符关系表")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class AuthMembers extends BaseEntity {
    private static final long serialVersionUID = -3767558043063304711L;


    // 加入 member 可以把 roleid 角色的成员关系赋予其它角色，则为真。
    @Column(name = "admin_option", nullable = false, comment = "是否继承")
    private Boolean adminOption = true;

    /**
     * 父auth
     */
    @ManyToOne
    @JoinColumn(name = "auth_id", nullable = false, foreignKey = @ForeignKey(name = "fk_auth_members_to_auth_id")
      ,comment = "父auth" )
    private Auth authRole;

    /**
     * 子auth
     */
    @ManyToOne
    @JoinColumn(name = "member_id", nullable = false, foreignKey = @ForeignKey(name = "fk_auth_members_to_member_id"),
            comment = "子auth")
    private Auth authMember;

    // 赋予此成员关系的角色的ID
    @ManyToOne
    @JoinColumn(name = "grantor_id", nullable = false, foreignKey = @ForeignKey(name = "fk_auth_members_to_grantor"),
    comment = "赋权角色")
    private Auth authGrantor;

}
