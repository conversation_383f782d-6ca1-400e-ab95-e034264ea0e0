package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

@Entity
@Table(name = "sys_cons_columns",comment = "约束包含列表")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class ConsColumns extends BaseEntity {
    private static final long serialVersionUID = 9096270217546239293L;

//
//    @Column(name = "CONSTRAINT_ID", nullable = false, length = 50)
//    private String constraintId;

//
//    @Column(name = "CON_TABLE_ID", nullable = false, length = 50)
//    private String conTableId;

//
//    @Column(name = "CON_F_TABLE_ID", nullable = false, length = 50)
//    private String conFTableId;

//
//    @Column(name = "CON_KEY_ID", nullable = false, length = 50)
//    private String conKeyId;

//
//    @Column(name = "CON_F_KEY_ID", nullable = false, length = 50)
//    private String conFKeyId;


    @Column(name = "position", nullable = false, precision = 0,comment = "顺序")
    private Integer position;

    @ManyToOne
    @JoinColumn(name = "constraint_id", foreignKey = @ForeignKey(name = "fk_cons_col_to_constraint_id"),comment = "约束")
    private Constraint constraint;

    @Column(name = "con_table_name", length = 50,comment = "约束表")
    private String conTableName;

    @Column(name = "con_f_table_name", length = 50,comment = "外建表")
    private String conFTableName;

    @Column(name = "con_key_name", length = 50,comment = "约束字段")
    private String conKeyName;

    @Column(name = "con_f_key_name", length = 50,comment = "外键字段")
    private String conFKeyName;


//    @ManyToOne
//    @Comment("约束表")
//    @JoinColumn(name = "con_table_id", referencedColumnName = "name", nullable = false, foreignKey = @ForeignKey(name = "fk_cons_col_to_con_table_id"))
//    private TableInfo tableInfo;
//
//    @ManyToOne
//    @Comment("外建表")
//    @JoinColumn(name = "con_f_table_id", referencedColumnName = "name", nullable = false, foreignKey = @ForeignKey(name = "fk_cons_col_to_con_f_t_id"))
//    private TableInfo fkTableInfo;
//
//    @ManyToOne
//    @Comment("约束字段")
//    @JoinColumn(name = "con_key_id", referencedColumnName = "name", nullable = false, foreignKey = @ForeignKey(name = "fk_cons_col_to_con_key_id"))
//    private FieldInfo fieldInfo;
//
//    @ManyToOne
//    @Comment("外键字段")
//    @JoinColumn(name = "con_f_key_id", referencedColumnName = "name", nullable = false, foreignKey = @ForeignKey(name = "fk_cons_col_to_con_f_key_id"))
//    private FieldInfo fkFieldInfo;

}
