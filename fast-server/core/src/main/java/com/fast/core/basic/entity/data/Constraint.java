package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.basic.entity.data.converter.ConFKActionTypeConverter;
import com.fast.core.basic.entity.data.converter.ConFMatchTypeConverter;
import com.fast.core.basic.entity.data.converter.ConstraintTypeConverter;
import com.fast.core.data.type.ConFKActionType;
import com.fast.core.data.type.ConFMatchType;
import com.fast.core.data.type.ConstraintType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import java.util.Set;

@Entity
@Table(name = "sys_constraint",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "con_table_name"})},
  comment = "约束表"

)
@Getter
@Setter
@NoArgsConstructor
@ToString
public class Constraint extends BaseEntity {
    private static final long serialVersionUID = -2342691159014871767L;

    @Column(name = "name", nullable = false, length = 400,comment = "约束名称")
    private String name;


    @Column(name = "field_name",comment = "字段名称")
    private String fieldName;


    @Convert(converter = ConstraintTypeConverter.class)
    @Column(name = "type", nullable = false, length = 1,comment = "约束类型")
    private ConstraintType type;


    @Column(name = "con_deferrable", nullable = false,comment = "约束是否可以推迟")
    private Boolean conDeferrable = false;


    @Column(name = "con_deferred", nullable = false,comment = "缺省时约束是否推迟")
    private Boolean conDeferred = false;

    @Column(name = "db", nullable = false,comment = "是否数据库约束")
    private Boolean db = false;

//
//    @Column(name = "CON_TABLE_ID", nullable = false, length = 50)
//    private String conTableId;

//
//    @Column(name = "CON_F_TABLE_ID", nullable = false, length = 50)
//    private String conFTableId;


    @Convert(converter = ConFKActionTypeConverter.class)
    @Column(name = "con_f_updtype", nullable = false, length = 1,comment = "外键更新动作")
    private ConFKActionType conFUpdtype;


    @Convert(converter = ConFKActionTypeConverter.class)
    @Column(name = "con_f_deltype", nullable = false, length = 1,comment = "外键删除动作")
    private ConFKActionType conFDeltype;


    @Convert(converter = ConFMatchTypeConverter.class)
    @Column(name = "con_f_matchtype", nullable = false, length = 1,comment = "外键匹配类型")
    private ConFMatchType conFMatchtype;


    @Column(name = "con_src", length = 3000,comment = "检查约束表达式")
    private String conSrc;

    @Column(name = "con_table_name", length = 50,comment = "约束表名称")
    private String conTableName;

    @Column(name = "con_f_table_name", length = 50,comment = "外键表名称")
    private String conFTableName;

//    @ManyToOne
//    @JoinColumn(name = "con_table_id", referencedColumnName = "name", nullable = false,
//            foreignKey = @ForeignKey(name = "fk_constraint_to_con_table_id"))
//    private TableInfo tableInfo;
//
//    @ManyToOne
//    @JoinColumn(name = "con_f_table_id", referencedColumnName = "name", nullable = false,
//            foreignKey = @ForeignKey(name = "fk_constraint_to_con_f_t_id"))
//    private TableInfo fkTableInfo;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "constraint_id")
    private Set<ConsColumns> consColumns;

}
