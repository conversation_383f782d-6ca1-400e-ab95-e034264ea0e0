package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.data.type.AscriptionType;
import com.fast.core.data.type.DataBaseType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import java.util.Set;

@Entity
@Table(name = "sys_data_base", comment = "数据库基本信息")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class DataBase extends BaseEntity {
    private static final long serialVersionUID = 6707423752571107414L;


    @Column(name = "name", nullable = false, length = 400, comment = "数据库名称")
    private String name;

    @Column(name = "code", nullable = false, length = 50, unique = true, comment = "数据库编码")
    private String code;

    @Column(name = "schema", nullable = true, length = 50, comment = "数据库schema")
    private String schema;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 50, comment = "数据库类型")
    private DataBaseType type;

    @Column(name = "url", nullable = false, length = 100, comment = "数据库链接url")
    private String url;

    @Column(name = "username", nullable = false, length = 50, comment = "用户名")
    private String username;

    @Column(name = "password", nullable = false, length = 50, comment = "密码")
    private String password;

    @Enumerated(EnumType.STRING)
    @Column(name = "ascription", nullable = false, length = 50, comment = "归属")
    private AscriptionType ascription;

    @Column(name = "table_prefix", length = 50, comment = "默认建表前缀")
    private String tablePrefix;


    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "data_base_id")
    private Set<TableInfo> tableInfos;

}
