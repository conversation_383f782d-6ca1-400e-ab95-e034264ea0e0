package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;


@Entity
@Table(name = "sys_dict",comment = "字典信息表")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class Dict extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "name",comment = "字典名称")
    private String name;

    @Column(name = "value",comment = "字典值")
    private String value;

    @Column(name = "code", unique = true, nullable = false,comment = "字典编码")
    private String code;

    @Column(name = "description",comment = "字典说明")
    private String description;

    @Column(name = "queue_order", precision = 5, scale = 0,comment = "排序号")
    private BigDecimal queueOrder;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", foreignKey = @ForeignKey(name = "fk_dict_to_parent_dict"))
    private Dict parent;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", updatable = false)
    @OrderBy(" queueOrder asc,id asc ")
    private Set<Dict> children = new HashSet<Dict>();


//    @Transient
//    public String getParentId() {
//        if (this.getParent() == null) {
//            return null;
//        }
//        return this.getParent().getId();
//    }
//
//    @Transient
//    public void setParentId(String id) {
//        Dict dict = new Dict();
//        dict.setId(id);
//        this.setParent(dict);
//    }


    public void addChildren(Dict children) {
        this.children.add(children);
    }

    public void removeChildren(Dict children) {
        this.children.remove(children);
    }

    public void addChildren(Collection<Dict> children) {
        this.children.addAll(children);
    }

    public void removeChildren(Collection<Dict> children) {
        this.children.removeAll(children);
    }


}
