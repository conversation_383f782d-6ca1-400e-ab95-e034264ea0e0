package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

@Entity
@Table(name = "sys_field_info",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "table_name"})},
  comment = "字段基本信息"
)

@Getter
@Setter
@NoArgsConstructor
@ToString
public class FieldInfo extends BaseEntity {
    private static final long serialVersionUID = 8250347759232745642L;

    @Column(name = "cn_name", nullable = false, length = 400,comment = "中文名称")
    private String cnName;

    @Column(name = "name", nullable = false, length = 400,comment = "字段名称")
    private String name;

    @Column(name = "len", nullable = false, precision = 0,comment = "长度")
    private Integer len = -1;


    @Column(name = "not_null", nullable = false, length = 1,comment = "是否为空")
    private Boolean notNull = false;


    @Column(name = "def_value", length = 400,comment = "默认值")
    private String defValue;


    @Column(name = "num", nullable = false, precision = 0,comment = "序号")
    private Integer num;


    @Column(name = "primary_key", nullable = false,comment = "是否主键")
    private Boolean primaryKey = false;

    @Column(name = "unique_item", nullable = false,comment = "是否唯一")
    private Boolean uniqueItem = false;


    @ManyToOne
    @JoinColumn(name = "table_name", referencedColumnName = "name", nullable = false, foreignKey = @ForeignKey(name = "fk_field_info_to_table_info"))
    private TableInfo tableInfo;

    @OneToOne
    @JoinColumn(name = "type_code", referencedColumnName = "code", nullable = false, foreignKey = @ForeignKey(name = "fk_field_info_to_field_type"))
    private FieldType fieldType;

    @Column(name="custom_content",comment = "自定义类型")
    @Lob @Basic(fetch = FetchType.LAZY)
    private String customContent;

    //TODO 只能这样存储了 有点乱
    //    @Column(name = "dict_code", length = 50,comment = "字典编码")
    //    private String dictCode;

}
