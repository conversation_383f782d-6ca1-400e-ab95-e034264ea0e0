package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.data.type.BasicDataType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

@Entity
@Table(name = "sys_field_type",comment = "字段类型")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class FieldType extends BaseEntity {
    private static final long serialVersionUID = 3339106750920114606L;

    @Column(name = "name", nullable = false, length = 400,comment = "名称")
    private String name;

    @Column(name = "code", nullable = false, unique = true,comment = "编码")
    private String code;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, precision = 0, comment = "类型")
    private BasicDataType type;

    @Column(name = "table_type",comment = "表类型")
    private String tableType;

    @Column(name = "len", nullable = false, precision = 0, comment = "长度")
    private Integer len;

    @Column(name = "check_rule", length = 3500,comment = "验证规则")
    private String checkRule;

    @Column(name = "input", length = 3500,comment = "入库规则")
    private String input;


    @Column(name = "output", length = 3500,comment = "出库规则")
    private String output;


    @Column(name = "java_type", nullable = false,comment = "java类型")
    private String javaType;

}
