package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2020-02-11 14:07
 **/

@Entity
@Table(name = "sys_file_info", comment = "文件信息")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class FileInfo extends BaseEntity {

    public FileInfo(String id, String fileName, String fileType, BigDecimal fileSize, LocalDateTime upDate) {
        this.id = id;
        this.fileName = fileName;
        this.fileType = fileType;
        this.fileSize = fileSize;
        this.upDate = upDate;
    }

    private static final long serialVersionUID = -3511761610245469198L;

    @Column(name = "up_person_name",comment = "上传人")
    private String upPersonName;

    @Column(name = "up_date",comment = "上传时间")
    private LocalDateTime upDate;

    // @Comment("上传部门")
    // private String upOrg;

    @Column(name = "type",comment = "附件类型")
    private String type;

    @Column(name = "file_type",comment = "文件后缀类型")
    private String fileType;

    @Column(name = "content_type",comment = "文件内容类型")
    private String contentType;

    @Column(name = "file_name",comment = "文件名称")
    private String fileName;

    @Column(name = "file_storage_path",comment = "文件存储路径")
    private String fileStoragePath;

    @Column(name = "file_path",comment = "文件路径")
    private String filePath;

    @Column(name = "file_size",comment = "文件大小")
    private BigDecimal fileSize;

}
