package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.data.type.DependencyType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "sys_inherits",comment = "表继承表")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class Inherits extends BaseEntity {
    private static final long serialVersionUID = -3029769047640884761L;


    @Column(name = "inh_t_name", nullable = false, length = 50,comment = "表名称")
    private String inhTName;


    @Column(name = "inh_parent_t_name", nullable = false, length = 50,comment = "父表名称")
    private String inhParentTName;


    @Column(name = "inh_seq_no", nullable = false, precision = 0,comment = "序号")
    private Integer inhSeqNo;


    @Column(name = "dep_type", nullable = false, length = 1,comment = "类型")
    private DependencyType depType = DependencyType.NORMAL;

}
