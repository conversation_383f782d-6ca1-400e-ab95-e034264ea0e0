package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.data.type.AclType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

@Entity
@Table(name = "sys_policy", comment = "范围权限表")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class Policy extends BaseEntity {
    private static final long serialVersionUID = 7675404726161532504L;

    @Column(name = "pol_cmd", nullable = false, precision = 0, comment = "权限")
    private Integer polCmd;

    @Column(name = "pol_role", nullable = false, length = 50, comment = "角色名称")
    private String polRole;

    @Column(name = "acl_type", nullable = false, comment = "ACL类型")
    @Enumerated(EnumType.STRING)
    private AclType aclType = AclType.AUTH;

    @Column(name = "pol_qual", length = 1000, comment = "表达式")
    private String polQual;

    @Column(name = "pol_table_name", length = 1000, comment = "表")
    private String polTableName;

    @Column(name = "acl_allow", nullable = false, comment = "是否允许")
    private Boolean allow = true;
}
