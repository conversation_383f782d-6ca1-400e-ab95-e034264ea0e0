package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.data.type.AuditType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import java.util.Set;

@Entity
@Table(name = "sys_table_info",comment = "表基本信息")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class TableInfo extends BaseEntity {
    private static final long serialVersionUID = 3717954346393602896L;

    @Column(name = "name", unique = true, nullable = false, length = 100,comment = "表名称")
    private String name;

    @Column(name = "cn_name", nullable = false, length = 400,comment = "中文名称")
    private String cnName;

    @Column(name = "has_sub_tables", nullable = false,comment = "是否存在子表")
    private Boolean hasSubTables = false;

    @Column(name = "has_parent_tables", nullable = false,comment = "是否存在父表")
    private Boolean hasParentTables = false;

    @Column(name = "has_column_security", nullable = false,comment = "是否存在列权限")
    private Boolean hasColumnSecurity = false;

    @Column(name = "has_row_security", nullable = false,comment = "是否存在行权限")
    private Boolean hasRowSecurity = false;

    @Column(name = "has_sign_row_security", nullable = false,comment = "是否存在单条行权限")
    private Boolean hasSignRowSecurity = false;

    @Column(name = "has_audit", nullable = false,comment = "是否审计表")
    private Boolean hasAudit = false;

    @Column(name = "audit_type",comment = "审计类型")
    @Enumerated(EnumType.STRING)
    private AuditType auditType = AuditType.AUTO_INCREMENT;

    // @Comment("表版本")
    // @Column(name = "version", nullable = false)
    // private Integer version;

    // @Comment("是否最高版本")
    // @Column(name = "max_version", nullable = false)
    // private Boolean maxVersion = false;

    // @Comment("是否最后编辑")
    // @Column(name = "last_edited", nullable = false)
    // private Boolean lastEdited = true;

    @Column(name = "status", nullable = false,comment = "表状态")
    private String status;

    @Column(name = "description", length = 2000,comment = "表说明")
    private String description;

    /**
     * 自身的外键
     */
    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "con_table_name", referencedColumnName = "name")
    private Set<Constraint> constraints;

    /**
     * 被其他表关联的外键
     */
    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "con_f_table_name", referencedColumnName = "name")
    private Set<Constraint> fkConstraints;

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "table_name", referencedColumnName = "name")
    @OrderBy(" num asc ")
    private Set<FieldInfo> fieldInfos;

    // @OneToMany(fetch = FetchType.LAZY)
    // @JoinColumn(name = "pol_table_name")
    // private Set<Policy> policies;
    //
    // @OneToMany
    // @JoinColumn(name = "acl_table_name")
    // private Set<AclInfo> acls;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "data_base_id", nullable = false, foreignKey = @ForeignKey(name = "fk_table_info_to_data_base"))
    private DataBase dataBase;

}
