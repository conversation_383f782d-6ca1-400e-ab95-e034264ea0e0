package com.fast.core.basic.entity.data;

import com.fast.core.basic.entity.BaseEntity;
import com.fast.core.rule.RuleType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

/**
 * @program: resource-shared
 * @description
 * @author: liunan
 * @create: 2019-06-10 09:42
 **/

@Entity
@Table(name = "sys_table_rule",comment = "表规则")
@Getter
@Setter
@NoArgsConstructor
@ToString
public class TableRule extends BaseEntity {

    private static final long serialVersionUID = -7589384467249732438L;

    @Column(name = "table_info_name", nullable = false,comment = "表名")
    private String tableInfoName;

    @Enumerated(EnumType.STRING)
    @Column(name = "rule_type", nullable = false,comment = "规则类型")
    private RuleType ruleType;

    @Lob @Basic(fetch = FetchType.LAZY)
    @Column(name = "content", nullable = false,comment = "规则内容")
    private String content;

}
