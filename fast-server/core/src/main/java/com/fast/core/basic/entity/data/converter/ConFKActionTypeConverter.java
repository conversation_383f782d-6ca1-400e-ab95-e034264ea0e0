package com.fast.core.basic.entity.data.converter;



import com.fast.core.data.type.ConFKActionType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class ConFKActionTypeConverter implements AttributeConverter<ConFKActionType, Character> {

    /**
     * Converts the value stored in the entity attribute into the
     * data representation to be stored in the database.
     *
     * @param attribute the entity attribute value to be converted
     * @return the converted data to be stored in the database column
     */
    @Override
    public Character convertToDatabaseColumn(ConFKActionType attribute) {
        return attribute.getValue();
    }

    /**
     * Converts the data stored in the database column into the
     * value to be stored in the entity attribute.
     * Note that it is the responsibility of the converter writer to
     * specify the correct dbData type for the corresponding column
     * for use by the JDBC driver: i.e., persistence providers are
     * not expected to do such type conversion.
     *
     * @param dbData the data from the database column to be converted
     * @return the converted value to be stored in the entity attribute
     */
    @Override
    public ConFKActionType convertToEntityAttribute(Character dbData) {
        return ConFKActionType.valueOf(dbData);
    }
}
