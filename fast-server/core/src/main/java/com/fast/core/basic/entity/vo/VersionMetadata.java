package com.fast.core.basic.entity.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: project-management-git
 * @description
 * @author: liunan
 * @create: 2022/3/11 11:16
 **/
@Data
@Builder
public class VersionMetadata implements Serializable {

    private String operator;
//    private RevisionMetadata.RevisionType revisionType;
    private Integer revision;
    private LocalDateTime updateDateTime;

}
