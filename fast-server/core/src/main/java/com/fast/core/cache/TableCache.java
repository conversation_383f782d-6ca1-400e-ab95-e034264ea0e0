package com.fast.core.cache;


import com.fast.core.data.TableMeta;

import java.util.List;

public interface TableCache {


    public List<TableMeta> getAllTable();

    /**
     * 获取表的字段、继承关系和权限
     *
     * @param name
     * @return
     */
    public TableMeta getTable(String name);

    /**
     * 初始化全部缓存
     */
    public void initTableVo();

    /**
     * 刷新某个目录的缓存
     *
     * @param name
     */
    public void refreshTableVo(String name);

    /**
     * 刷新所有目录的缓存
     */
    public void refreshAllTableVo();

    /**
     * 刷新目录的Acl权限
     * @param name
     */
    public void refreshTableAcl(String name);

    /**
     * 刷新所有目录的权限
     */
    public void refreshAllTableAcl();
}
