package com.fast.core.cache;


import com.fast.core.data.TableMeta;

import java.util.ArrayList;
import java.util.List;

public class TableCacheUtils {

    private TableCache tableCache;

    private AuthCache authCache;

    private static TableCache staticTableCache;

    private static AuthCache staticAuthCache;

    public void init() {
        staticTableCache = tableCache;
        staticAuthCache = authCache;
    }

    /**
     * 获取表的字段、继承关系和权限
     *
     * @param name
     * @return
     */
    public static TableMeta getTable(String name) {
        return staticTableCache.getTable(name);
    }


    /**
     * 获取 code 所有权限
     *
     * @param code
     * @return
     */
    public static List<String> getAuthInherit(String code) {
        List<String> result = new ArrayList<>();
        staticAuthCache.getAuthInherit(code);
        return result;
    }


}
