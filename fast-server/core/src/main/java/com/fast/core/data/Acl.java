package com.fast.core.data;


import com.fast.core.cache.TableCacheUtils;
import com.fast.core.rule.GenericPurviewBase;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fast.core.rule.GenericPurviewBase.*;


/**
 * 反问权限列表
 * 每种权限的列表
 */
@Data
public class Acl implements Serializable {

    private static final long serialVersionUID = -4902162041549712297L;

    private Map<String, Integer> allowMap = new HashMap<>();

    private Map<String, Integer> forbidMap = new HashMap<>();


    public void addAllow(String auth, Integer privilege) {
        allowMap.merge(auth, privilege, (a, b) -> sumPrivilege(a, b));
    }

    public void removeAllow(String auth, Integer privilege) {
        allowMap.merge(auth, privilege, GenericPurviewBase::delPrivilege);
    }

    public void addForbid(String auth, Integer privilege) {
        forbidMap.merge(auth, privilege, (a, b) -> sumPrivilege(a, b));
    }

    public void removeForbid(String auth, Integer privilege) {
        forbidMap.merge(auth, privilege, GenericPurviewBase::delPrivilege);
    }

    public boolean isAllowIncludeAuth(String auth) {
        return allowMap.containsKey(auth);
    }

    public boolean isForbidIncludeAuth(String auth) {
        return forbidMap.containsKey(auth);
    }

    public boolean isAllow(int privilege, String auth) {
        return assertAuth(allowMap, privilege, auth);
    }


    public boolean isForbid(int privilege, String auth) {
        //判断授权情况,先查看本地授权
        //找不到则查询，角色的全部继承 从高到低
        //判断角色是否有权限
        return assertAuth(forbidMap, privilege, auth);
    }

    //合并另外一个Acl，如果存在则忽略，如果不存在则add
    public void mergeAcl(Acl acl) {
        if (acl != null) {
            Map<String, Integer> forbid = acl.forbidMap;
            Map<String, Integer> allow = acl.allowMap;
            allow.forEach((key, value) -> {
                if (this.allowMap.containsKey(key)) {
                    // 忽略
                } else {
                    addAllow(key, value);
                }
            });
            forbid.forEach((key, value) -> {
                if (this.forbidMap.containsKey(key)) {
                    // 忽略
                } else {
                    addForbid(key, value);
                }
            });

        }
    }


    private boolean assertAuth(Map<String, Integer> authMap, int privilege, String auth) {
        boolean allow = false;
        Integer rule = authMap.get(auth);
        if (rule == null) {
            rule = 0;
        }
        switch (privilege) {
            case QUERY_OR_USE_PRIVILEGE:
                allow = checkQueryOrUsePrivilege(rule);
                break;
            case CREATE_PRIVILEGE:
                allow = checkCreatePrivilege(rule);
                break;
            case DELETE_PRIVILEGE:
                allow = checkDeletePrivilege(rule);
                break;
            case UPDATE_PRIVILEGE:
                allow = checkUpdatePrivilege(rule);
                break;
            case DECRYPT_PRIVILEGE:
                allow = checkDecryptPrivilege(rule);
                break;
            case NO_SIGN_SENSITIVE_PRIVILEGE:
                allow = checkNoSignSensitivePrivilege(rule);
                break;
            case NO_MANY_SENSITIVE_PRIVILEGE:
                allow = checkNoManySensitivePrivilege(rule);
                break;
            default:
                break;
        }
        if (!allow) {
            List<String> authList = TableCacheUtils.getAuthInherit(auth);
            for (String item : authList) {
                allow = assertAuth(authMap, privilege, item);
                if (allow) {
                    break;
                }
            }
        }
        return allow;
    }
}
