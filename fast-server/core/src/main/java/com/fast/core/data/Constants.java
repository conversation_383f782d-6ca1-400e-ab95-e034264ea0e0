package com.fast.core.data;


import java.util.Arrays;

public class Constants {

  public static final String INNHERITS_PREFIX = "VI_";

  public static final String ID = "id";
  public static final String CREATE_USER_ID = "create_user_id";
  public static final String CREATOR = "creator";
  public static final String CREATE_DATE = "create_date";
  public static final String MODIFY_USER_ID = "modify_user_id";
  public static final String MODIFIER = "modifier";
  public static final String MODIFY_DATE = "modify_date";
  public static final String ORG_ID = "org_id";
  public static final String BELONG_ID = "belong_id";


  public static final String[] DEFAULT_FIELD = {ID, CREATE_USER_ID, CREATOR, CREATE_DATE, MODIFY_USER_ID, MODIFIER, MODIFY_DATE, BELONG_ID};

  public static boolean isDefaultField(String fieldName) {
    return Arrays.asList(DEFAULT_FIELD).contains(fieldName);
  }

}
