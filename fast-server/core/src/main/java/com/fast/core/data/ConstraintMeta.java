package com.fast.core.data;

import com.fast.core.data.type.ConFKActionType;
import com.fast.core.data.type.ConFMatchType;
import com.fast.core.data.type.ConstraintType;
import com.fast.core.data.type.JoinType;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 主键约束，检查约束，唯一约束
 * 外建约束
 */
@Data
@Builder
public class ConstraintMeta implements Serializable {

    private static final long serialVersionUID = -6872717191827293848L;

    private String name;
    private String fieldName;
    private ConstraintType type;
    @Builder.Default
    private boolean conDeferrable = false;
    @Builder.Default
    private boolean conDeferred = false;
    //关联的tableName
    private String conTable;
    //关联的tableName
    private String conFTable;
    private List<ConsColumnsMeta> consColumns;
    @Builder.Default
    private ConFKActionType conFUpdType = ConFKActionType.NO_ACTION;
    @Builder.Default
    private ConFKActionType conFDelType = ConFKActionType.NO_ACTION;
    @Builder.Default
    private ConFMatchType conFMatchtype = ConFMatchType.FULL;
    private String conSrc;
    @Builder.Default
    private JoinType joinType = JoinType.NONE;


}
