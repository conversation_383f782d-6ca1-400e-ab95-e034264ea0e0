package com.fast.core.data;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class FieldMeta implements Serializable {
    private static final long serialVersionUID = 1580453331477299889L;

    private String id;
    private String name;
    /**
     * 数据库名称
     */
    private String dbName;
    private String cnName;

    private int len;
    private boolean notNull;
    private String defValue;
    private int num;
    private String dictCode;

    private FieldTypeMeta fieldTypeMeta;

    private Acl acl;

    public boolean isAllow(int privilege, String auth) {
        return acl.isAllow(privilege, auth);
    }

}
