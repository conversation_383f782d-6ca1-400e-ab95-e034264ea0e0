package com.fast.core.data;

import com.fast.core.data.type.BasicDataType;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class FieldTypeMeta implements Serializable {

    private static final long serialVersionUID = 7410077848613076191L;

    private String name;
    private String code;
    private BasicDataType type;
    private String tableType;
    private int len;
    private String checkRule;
    private String input;
    private String output;
    private String javaType;

}

