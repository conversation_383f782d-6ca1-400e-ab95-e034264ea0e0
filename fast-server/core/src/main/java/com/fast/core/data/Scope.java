package com.fast.core.data;


import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static com.fast.core.rule.GenericPurviewBase.*;

/**
 * 每个目录都有scope，用来控制数据
 * 一个目录会有多个 policy 和 多个 AclData 整合到一起。
 * 权限规则 跟这个如何结合？
 */
public class Scope implements Serializable {

    private static final long serialVersionUID = 8651433669833856325L;

    private Map<String, ScopeCondition> queryOrUser = new HashMap<>();  // 查看权限

    private Map<String, ScopeCondition> create = new HashMap<>(); //添加权限

    private Map<String, ScopeCondition> delete = new HashMap<>(); //删除权限

    private Map<String, ScopeCondition> update = new HashMap<>(); //更新权限

    private Map<String, ScopeCondition> decrypt = new HashMap<>(); //解密权限

    private Map<String, ScopeCondition> noSignSensitive = new HashMap<>(); //不脱敏权限

    private Map<String, ScopeCondition> noManySensitive = new HashMap<>(); //不脱敏权限


    public ScopeCondition isAllow(int privilege, String auth) {
        //判断授权情况,先查看本地授权
        //找不到则查询，角色的全部继承 从高到低
        //判断角色是否有权限
        ScopeCondition condition = null;
        switch (privilege) {
            case QUERY_OR_USE_PRIVILEGE:
                condition = allow(queryOrUser, auth);
                break;
            case CREATE_PRIVILEGE:
                condition = allow(create, auth);
                break;
            case DELETE_PRIVILEGE:
                condition = allow(delete, auth);
                break;
            case UPDATE_PRIVILEGE:
                condition = allow(update, auth);
                break;
            case DECRYPT_PRIVILEGE:
                condition = allow(decrypt, auth);
                break;
            case NO_SIGN_SENSITIVE_PRIVILEGE:
                condition = allow(noSignSensitive, auth);
                break;
            case NO_MANY_SENSITIVE_PRIVILEGE:
                condition = allow(noManySensitive, auth);
                break;
            default:
                break;
        }
        return condition;
    }


    private ScopeCondition allow(Map<String, ScopeCondition> conditionMap, String auth) {
        ScopeCondition condition = conditionMap.get(auth);
        if (condition == null) {
            return new ScopeCondition();
        }
        return condition;
    }


    public void addQueryOrUser(String auth, boolean isAllow, String condition) {
        addScopeCondition(auth, isAllow, condition, queryOrUser);
    }

    public void addCreate(String auth, boolean isAllow, String condition) {
        addScopeCondition(auth, isAllow, condition, create);
    }

    public void addDelete(String auth, boolean isAllow, String condition) {
        addScopeCondition(auth, isAllow, condition, delete);
    }

    public void addUpdate(String auth, boolean isAllow, String condition) {
        addScopeCondition(auth, isAllow, condition, update);
    }

    public void addDecrpt(String auth, boolean isAllow, String condition) {
        addScopeCondition(auth, isAllow, condition, decrypt);
    }

    public void addNoSignSensitive(String auth, boolean isAllow, String condition) {
        addScopeCondition(auth, isAllow, condition, noSignSensitive);
    }

    public void addNoManySensitive(String auth, boolean isAllow, String condition) {
        addScopeCondition(auth, isAllow, condition, noManySensitive);
    }

    private void addScopeCondition(String auth, boolean isAllow, String condition, Map<String, ScopeCondition> scopeConditionMap) {
        ScopeCondition scopeCondition = noManySensitive.get(auth);
        if (scopeCondition == null) {
            scopeCondition = new ScopeCondition();
        }
        if (isAllow) {
            scopeCondition.addAllow(condition);
        } else {
            scopeCondition.addForbid(condition);
        }
        scopeConditionMap.put(auth, scopeCondition);
    }

}
