package com.fast.core.data;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: resource-shared
 * @description
 * @author: liunan
 * @create: 2019-06-14 11:05
 **/
@Data
public class ScopeCondition implements Serializable {

    private static final long serialVersionUID = -5206961634307740705L;

    private List<String> allow = new ArrayList<>();

    private List<String> forbid = new ArrayList<>();


    public void addAllow(String condition) {
        allow.add(condition);
    }

    public void addAllows(List<String> conditions) {
        allow.addAll(conditions);
    }

    public void addForbid(String condition) {
        forbid.add(condition);
    }

    public void addForbids(List<String> conditions) {
        forbid.addAll(conditions);
    }

}
