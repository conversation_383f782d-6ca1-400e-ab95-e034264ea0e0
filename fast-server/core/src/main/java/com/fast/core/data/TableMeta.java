package com.fast.core.data;


import com.fast.core.data.type.AuditType;
import com.fast.core.cache.TableCacheUtils;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

@Data
@Builder
public class TableMeta implements Serializable {

  private static final long serialVersionUID = 175539124697685590L;

  private String id;

  /**
   * 表名
   */
  private String name;

  /**
   * 数据库名称
   */
  private String dbName;

  /**
   * 表名-中文
   */
  private String cnName;

  /**
   * 是否存在子表
   */
  @Builder.Default
  private boolean hasSubTables = false;

  /**
   * 是否存在父表
   */
  @Builder.Default
  private boolean hasParentTables = false;

  /**
   * 是否有列权限过滤
   */
  @Builder.Default
  private boolean hasColumnSecurity = false;

  /**
   * 是否有行权限过滤
   */
  @Builder.Default
  private boolean hasRowSecurity = false;

  /**
   * 是否存在单行权限过滤
   */
  @Builder.Default
  private boolean hasSignRowSecurity = false;

  /**
   * 是否审计表
   */
  @Builder.Default
  private Boolean hasAudit = false;

  /**
   * 审计类型
   */
  @Builder.Default
  private AuditType auditType = AuditType.AUTO_INCREMENT;

  /**
   * 字段列表
   */
  @Builder.Default
  private LinkedHashMap<String, FieldMeta> fieldMap = new LinkedHashMap<>();

  /**
   * 所有的字段列表，包括引用字段
   */
  @Builder.Default
  private LinkedHashMap<String, FieldMeta> allFieldMap = new LinkedHashMap<>();

  /**
   * 继承表的关系
   */
  @Builder.Default
  private Map<String, Character> parentNames = new HashMap<>();

  /**
   * 继承的表
   */
  @Builder.Default
  private List<TableMeta> parentTables = new ArrayList<>();


  /**
   * 子表的关系
   */
  @Builder.Default
  private Map<String, Character> subNames = new HashMap<>();


  /**
   * 子表
   */
  @Builder.Default
  private List<TableMeta> subTables = new ArrayList<>();


  /**
   * 关联表
   */
  @Builder.Default
  private Map<String, TableMeta> reactionTableMap = new HashMap<>();


  /**
   * 表权限
   */
  private Acl acl;


  /**
   * 列权限
   */
  private Scope scope;

  /**
   * 主键约束，检查约束，唯一约束,外键约束
   */
  @Builder.Default
  private List<ConstraintMeta> constraintMetaList = new ArrayList<>();


  /**
   * 获取范围权限
   *
   * @param privilege
   * @param auth
   * @return
   */
  public ScopeCondition getScope(int privilege, String auth) {
    return scope.isAllow(privilege, auth);
  }

  /**
   * 字段权限,当前的字段
   *
   * @param fieldName
   * @param privilege
   * @param auth
   * @return
   */
  public boolean isFieldAllow(String fieldName, int privilege, String auth) {
    //根据fieldName 获取 Field
    FieldMeta fieldMeta = fieldMap.get(fieldName);
    return fieldMeta.isAllow(privilege, auth);
  }


  /**
   * 字段权限，所有的字段
   *
   * @param fieldName
   * @param privilege
   * @param auth
   * @return
   */
  public boolean isAllFieldAllow(String fieldName, int privilege, String auth) {
    //根据fieldName 获取 Field
    FieldMeta fieldMeta = allFieldMap.get(fieldName);
    return fieldMeta.isAllow(privilege, auth);
  }


  /**
   * 表权限
   *
   * @param privilege
   * @param auth
   * @return
   */
  public boolean isAllow(int privilege, String auth) {
    return acl.isAllow(privilege, auth);
  }

  public boolean fieldContains(String name) {
    return fieldMap.keySet().stream().anyMatch((v) -> v.equals(name));
  }


  public TableMeta getReactionTable(String name) {
    return reactionTableMap.get(name);
  }

  public TableMeta putReactionTable(String name, TableMeta tableMeta) {
    return reactionTableMap.put(name, tableMeta);
  }

  public List<TableMeta> getParentTables() {
    if (parentTables == null && parentNames != null) {
      parentTables = listTableVo(parentNames.keySet());
    }
    return parentTables;
  }

  public List<TableMeta> getSubTables() {
    if (subTables == null && subNames != null) {
      subTables = listTableVo(subNames.keySet());
    }
    return subTables;
  }


  private List<TableMeta> listTableVo(Set<String> tableNames) {
    List<TableMeta> resultTables = new ArrayList<>();
    for (String item : tableNames) {
      if (StringUtils.isNotBlank(item)) {
        TableMeta tableMeta = TableCacheUtils.getTable(item);
        assert tableMeta != null : "tableVo 不能为空。";
        resultTables.add(tableMeta);
      }
    }
    return resultTables;
  }

  public ConstraintMeta getConstraint(String fieldName) {
    Optional<ConstraintMeta> optional = this.constraintMetaList.stream().filter(item -> StringUtils.isNotBlank(item.getFieldName()) && item.getFieldName().equals(fieldName)).findFirst();
    return optional.orElse(null);
  }

  public FieldMeta getField(String fieldName) {
    return this.allFieldMap.get(fieldName);
  }

}
