package com.fast.core.data.type;


/**
 * a = 无动作， r = 限制， c = 级联， n = 置空， d = 置为默认值
 */
public enum ConFKActionType implements EnumValue {
  NO_ACTION('a'), RESTRICT('r'), CASCADE('c'), NULL('n'), DEFAULT('d');

  private final char value;

  private ConFKActionType(char value) {
    this.value = value;
  }

  public static ConFKActionType valueOf(char value) {
    for (ConFKActionType r : ConFKActionType.values()) {
      if (r.getValue() == value) {
        return r;
      }
    }
    return null;
  }

  @Override
  public char getValue() {
    return this.value;
  }

  @Override
  public String toString() {
    return this.value + "";
  }

  public static String serialize(ConFKActionType value) {
    return value.toString();
  }

  public static ConFKActionType deserialize(String value) {
    return ConFKActionType.valueOf(value);
  }
}
