package com.fast.core.data.type;

/**
 * f = 完全， p = 部分， s = 简单
 */
public enum ConFMatchType implements EnumValue {
    FULL('f'), PART('p'), SIMPLE('s');

    private final char value;

    private ConFMatchType(char value) {
        this.value = value;
    }

    public static ConFMatchType valueOf(char value) {
        for (ConFMatchType r : ConFMatchType.values()) {
            if (r.getValue() == value) {
                return r;
            }
        }
        return null;
    }

    @Override
    public char getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.value+"";
    }
}
