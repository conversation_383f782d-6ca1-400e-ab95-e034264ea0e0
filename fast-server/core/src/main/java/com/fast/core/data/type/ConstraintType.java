package com.fast.core.data.type;


/**
 * P 主键 F 外建 U 唯一 C 检查 I 无效外键
 */
public enum ConstraintType implements EnumValue {
    PRIMARY_KEY('P'), FOREIGN_KEY('F'), UNIQUE('U'), CHECK('C'), FOREIGN_KEY_INVALID('I');

    private final char value;

    private ConstraintType(char value) {
        this.value = value;
    }


    public static ConstraintType valueOf(char value) {
        for (ConstraintType r : ConstraintType.values()) {
            if (r.getValue() == value) {
                return r;
            }
        }
        return null;
    }

    @Override
    public char getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.value + "";
    }
}
