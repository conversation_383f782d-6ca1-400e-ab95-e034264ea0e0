package com.fast.core.data.type;


public enum DependencyType implements EnumValue {
    NORMAL('n'), AUTO('a');

    private final char value;

    private DependencyType(char value) {
        this.value = value;
    }

    public static DependencyType valueOf(char value) {
        for (DependencyType r : DependencyType.values()) {
            if (r.getValue() == value) {
                return r;
            }
        }
        return null;
    }

    @Override
    public char getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.value + "";
    }
}
