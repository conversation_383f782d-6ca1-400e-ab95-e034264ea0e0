package com.fast.core.data.utils;

import com.fast.core.basic.entity.data.TableInfo;
import com.fast.core.data.FieldMeta;
import com.fast.core.data.FieldTypeMeta;
import com.fast.core.data.TableMeta;
import com.fast.core.data.type.BasicDataType;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;

import static com.fast.core.data.utils.TableDictionary.DEFAULT_EMPTY;
import static com.fast.core.data.utils.TableDictionary.UUID;

public class DefaultMetadata {

  public static final FieldTypeMeta CUSTOM_TYPE = FieldTypeMeta.builder()
    .code(BasicDataType.FIELD_EMPTY.name())
    .name("自定义")
    .type(BasicDataType.FIELD_EMPTY)
    .len(-1)
    .javaType("java.lang.String")
    .build();

  public static final FieldTypeMeta PRIMARY_KEY_TYPE = FieldTypeMeta.builder()
    .name("系统标示（UUID）")
    .code(UUID)
    .type(BasicDataType.FIELD_STRING)
    .len(50)
    .javaType("java.lang.String")
    .build();


  public static final FieldTypeMeta DICT_TYPE = FieldTypeMeta.builder()
    .name("字典")
    .code(TableDictionary.DICT)
    .type(BasicDataType.FIELD_STRING)
    .len(50)
    .javaType("java.lang.String")
    .build();

  public static final FieldTypeMeta CODE_TYPE = FieldTypeMeta.builder()
    .name("CODE或ID")
    .code(TableDictionary.CODE)
    .type(BasicDataType.FIELD_STRING)
    .len(50)
    .javaType("java.lang.String")
    .build();

  public static final FieldTypeMeta SHORT_TEXT_TYPE = FieldTypeMeta.builder()
    .name("短字符")
    .code(TableDictionary.SHORT_TEXT)
    .type(BasicDataType.FIELD_STRING)
    .len(255)
    .javaType("java.lang.String")
    .build();

  public static final FieldTypeMeta TEXT_TYPE = FieldTypeMeta.builder()
    .name("字符")
    .code("TEXT")
    .type(BasicDataType.FIELD_STRING)
    .len(600)
    .javaType("java.lang.String")
    .build();

  public static final FieldTypeMeta LONG_TEXT_TYPE = FieldTypeMeta.builder()
    .name("长字符")
    .code("LONG_TEXT")
    .type(BasicDataType.FIELD_STRING)
    .len(3800)
    .javaType("java.lang.String")
    .build();

  public static final FieldTypeMeta EXTRA_LONG_TEXT_TYPE = FieldTypeMeta.builder()
    .name("超长字符")
    .code("EXTRA_LONG_TEXT")
    .type(BasicDataType.FIELD_LOB)
    .len(-1)
    .javaType("java.lang.String")
    .build();


  public static final FieldTypeMeta BOOLEAN_TYPE = FieldTypeMeta.builder()
    .name("是否")
    .code("BOOLEAN")
    .type(BasicDataType.FIELD_BOOLEAN)
    .len(0)
    .javaType("java.lang.Boolean")
    .build();

  public static final FieldTypeMeta NUMBER_TYPE = FieldTypeMeta.builder()
    .name("数量")
    .code("AMOUNT")
    .type(BasicDataType.FIELD_BIG_DECIMAL)
    .len(-1)
    .javaType("java.math.BigDecimal")
    .build();

  public static final FieldTypeMeta DATE_TYPE = FieldTypeMeta.builder()
    .name("日期")
    .code("DATE")
    .type(BasicDataType.FIELD_DATE)
    .len(-1)
    .javaType("java.time.LocalDate")
    .build();

  public static final FieldTypeMeta DATETIME_TYPE = FieldTypeMeta.builder()
    .name("日期时间")
    .code(TableDictionary.DATETIME)
    .type(BasicDataType.FIELD_DATE_TIME)
    .len(-1)
    .javaType("java.time.LocalDateTime")
    .build();


  public static final FieldMeta EMPTY_FIELD = FieldMeta.builder()
    .name(DEFAULT_EMPTY)
    .cnName("空字段")
    .defValue("")
    .len(50)
    .notNull(false)
    .num(0)
    .dbName(DEFAULT_EMPTY)
    .fieldTypeMeta(CUSTOM_TYPE)
    .build();

  public static final TableMeta EMPTY_TABLE = TableMeta.builder()
    .name(DEFAULT_EMPTY)
    .cnName("空表")
    .dbName(DEFAULT_EMPTY)
    .fieldMap(new LinkedHashMap<>(Collections.singletonMap(DEFAULT_EMPTY, EMPTY_FIELD)))
    .allFieldMap(new LinkedHashMap<>(Collections.singletonMap(DEFAULT_EMPTY, EMPTY_FIELD)))
    .build();


}


