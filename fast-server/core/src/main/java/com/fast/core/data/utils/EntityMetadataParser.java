package com.fast.core.data.utils;

import com.fast.core.data.*;
import com.fast.core.data.type.BasicDataType;
import com.fast.core.data.type.ConstraintType;
import com.fast.tools.utils.StringUtils;
import jakarta.persistence.*;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fast.core.data.Constants.ID;
import static com.fast.core.data.utils.DefaultMetadata.*;
import static com.fast.core.data.utils.TableDictionary.DEFAULT_EMPTY;

public class EntityMetadataParser {

  public static final Map<String, FieldTypeMeta> tableFieldTypeMap = new HashMap<>();

  /**
   * 初始化表Meta
   *
   * @param entityClass
   * @return
   */
  public static TableMeta parseEntity(Class<?> entityClass) {

    Table annTable = entityClass.getAnnotation(Table.class);
    String tableName = annTable.name();
    FieldTypeMeta tableSetType = generateFieldTypeByTableSet(tableName);
    FieldTypeMeta tableType = generateFieldTypeByTable(tableName);

    tableFieldTypeMap.put(tableSetType.getName(), tableType);
    tableFieldTypeMap.put(tableType.getName(), tableType);

    String tableCnName = annTable.comment();

    // 字段
    List<FieldMeta> fieldMetas = new ArrayList<>();
    List<FieldMeta> allFieldMetas = new ArrayList<>();

    // 关联类型
    List<ConstraintMeta> constraintMetaList = new ArrayList<>();

    List<Field> declaredFields = getFields(entityClass);
    int i = 0;
    for (Field field : declaredFields) {
      if (Modifier.isStatic(field.getModifiers()) && Modifier.isFinal(field.getModifiers())) {
        continue;
      }
      String key = field.getName();
      // 添加主键Constraint
      if (field.isAnnotationPresent(Id.class)) {
        Column fieldColumn = field.getAnnotation(Column.class);
        String fieldDbName = (fieldColumn != null && StringUtils.isNotBlank(fieldColumn.name())) ? fieldColumn.name() : key.toLowerCase();
        ConstraintMeta constraintMeta = ConstraintMeta.builder()
          .name(tableName + "_pkey")
          .fieldName(field.getName())
          .type(ConstraintType.PRIMARY_KEY)
          .conTable(tableName)
          .conFTable(DEFAULT_EMPTY)
          .consColumns(Collections.singletonList(ConsColumnsMeta.builder()
            .field(field.getName())
            .dbField(fieldDbName)
            .fkField(DEFAULT_EMPTY)
            .dbFkField(DEFAULT_EMPTY)
            .build())
          ).build();
        constraintMetaList.add(constraintMeta);
      }
      // 处理字段和关联关系
      if (field.isAnnotationPresent(OneToOne.class) || field.isAnnotationPresent(ManyToOne.class)) {
        JoinColumn joinColumn = field.getAnnotation(JoinColumn.class);
        String fieldName = joinColumn != null ? joinColumn.name() : key.toUpperCase();
        FieldMeta relationField = FieldMeta.builder()
          .name(fieldName)
          .cnName(fieldName)
          .defValue("")
          .len(50)
          .notNull(false)
          .num(i)
          .dbName(fieldName)
          .fieldTypeMeta(PRIMARY_KEY_TYPE)
          .build();
        allFieldMetas.add(relationField);
        i++;

        Table annTableType = field.getType().getAnnotation(Table.class);
        String tableTypeName = annTableType.name();
        FieldMeta objectField = FieldMeta.builder()
          .name(field.getName())
          .cnName(field.getName())
          .defValue("")
          .len(0)
          .notNull(false)
          .num(i)
          .dbName(field.getName())
          .fieldTypeMeta(getTableFieldTypeOrGenerate(tableTypeName))
          .build();
        fieldMetas.add(objectField);
        allFieldMetas.add(objectField);
        i++;
        // 处理外键
        constraintMetaList.addAll(parseConstraint(tableName, field));
      } else if (field.isAnnotationPresent(OneToMany.class) || field.isAnnotationPresent(ManyToMany.class)) {
        ParameterizedType type = (ParameterizedType) field.getGenericType();
        Class<?> tableClass = (Class<?>) type.getActualTypeArguments()[0];
        Table annTableType = tableClass.getAnnotation(Table.class);
        String tableTypeName = annTableType.name();
        FieldMeta setObjectField = FieldMeta.builder()
          .name(key)
          .cnName(key)
          .defValue("")
          .len(0)
          .notNull(false)
          .num(i)
          .dbName(key)
          .fieldTypeMeta(getTableSetFieldTypeOrGenerate(tableTypeName))
          .build();
        fieldMetas.add(setObjectField);
        allFieldMetas.add(setObjectField);
        i++;
        // 处理外键
        constraintMetaList.addAll(parseConstraint(tableName, field));
      } else {
        Column fieldColumn = field.getAnnotation(Column.class);
        String fieldCnName = (fieldColumn != null && StringUtils.isNotBlank(fieldColumn.comment())) ? fieldColumn.comment() : key;
        String dbFieldName = fieldColumn != null && StringUtils.isNotBlank(fieldColumn.name()) ? fieldColumn.name() : key.toLowerCase();
        FieldTypeMeta fieldTypeMeta = getFieldTypeByType(field.getType());
        FieldMeta fieldMeta = FieldMeta.builder()
          .name(field.getName())
          .cnName(fieldCnName)
          .defValue("")
          .len(fieldTypeMeta.getLen())
          .notNull(fieldColumn != null && !fieldColumn.nullable())
          .num(i)
          .dbName(dbFieldName)
          .fieldTypeMeta(fieldTypeMeta)
          .build();
        fieldMetas.add(fieldMeta);
        allFieldMetas.add(fieldMeta);
        i++;
      }
    }

    LinkedHashMap<String, FieldMeta> fieldMeta = new LinkedHashMap<>(fieldMetas
      .stream()
      .collect(Collectors.toMap(FieldMeta::getName, f -> f)));
    LinkedHashMap<String, FieldMeta> allFieldMeta = new LinkedHashMap<>(allFieldMetas
      .stream()
      .collect(Collectors.toMap(FieldMeta::getName, f -> f)));

    return TableMeta.builder()
      .name(tableName)
      .cnName(tableCnName)
      .dbName(tableName)
      .fieldMap(fieldMeta)
      .allFieldMap(allFieldMeta)
      .constraintMetaList(constraintMetaList)
      .build();
  }


  private static List<ConstraintMeta> parseConstraint(String tableName, Field propertyField) {

    List<ConstraintMeta> constraintMetas = new ArrayList<>();
    if (propertyField != null && (propertyField.isAnnotationPresent(ManyToOne.class)
      || propertyField.isAnnotationPresent(OneToOne.class)
    )) {
      JoinColumn joinColumn = propertyField.getAnnotation(JoinColumn.class);
      Class<?> fkClassName = propertyField.getType();
      Table fkTable = fkClassName.getAnnotation(Table.class);
      String fkTableName = fkTable.name();
      String fieldDbName = joinColumn.name();
      String fkFieldDbName = StringUtils.isNotBlank(joinColumn.referencedColumnName()) ? joinColumn.referencedColumnName() : ID;
      String fkName = TableDictionary.generateFkName(tableName, fkTableName, propertyField.getName());
      ConstraintMeta constraintMeta = ConstraintMeta.builder()
        .name(fkName)
        .fieldName(propertyField.getName())
        .type(ConstraintType.FOREIGN_KEY)
        .conTable(tableName)
        .conFTable(fkTableName)
        .consColumns(Collections.singletonList(ConsColumnsMeta.builder()
          .field(fieldDbName)
          .dbField(fieldDbName)
          .fkField(fkFieldDbName)
          .dbFkField(fkFieldDbName)
          .build()))
        .build();
      constraintMetas.add(constraintMeta);
    }
    if (propertyField != null && (propertyField.isAnnotationPresent(OneToMany.class))) {
      ParameterizedType parameterizedType = (ParameterizedType) propertyField.getGenericType();
      Class<?> fkTableClass = (Class<?>) parameterizedType.getActualTypeArguments()[0];
      Table fkTable = fkTableClass.getAnnotation(Table.class);
      String fkTableName = fkTable.name();
      JoinColumn joinColumn = propertyField.getAnnotation(JoinColumn.class);
      if (joinColumn == null) {
        throw new RuntimeException("OneToMany 必须配置 JoinColumn");
      }
      String idFieldName = StringUtils.isNotBlank(joinColumn.referencedColumnName()) ? joinColumn.referencedColumnName() : ID;
      String fkFieldName = joinColumn.name();
      String fkName = TableDictionary.generateFkName(tableName, fkTableName, propertyField.getName());
      ConstraintMeta constraintMeta = ConstraintMeta.builder()
        .name(fkName)
        .fieldName(propertyField.getName())
        .type(ConstraintType.FOREIGN_KEY_INVALID)
        .conTable(tableName)
        .conFTable(fkTableName)
        .consColumns(Collections.singletonList(ConsColumnsMeta.builder()
          .field(idFieldName)
          .dbField(idFieldName)
          .fkField(fkFieldName)
          .dbFkField(fkFieldName)
          .build()))
        .build();
      constraintMetas.add(constraintMeta);
    }
    return constraintMetas;
  }


  public static FieldTypeMeta generateFieldTypeByTable(String tableName) {
    String objectTypeCode = TableDictionary.generateObjectType(tableName);
    return FieldTypeMeta.builder()
      .name(objectTypeCode)
      .code(objectTypeCode)
      .type(BasicDataType.FIELD_EMPTY)
      .tableType(tableName)
      .len(0)
      .javaType(Object.class.getName())
      .build();
  }

  public static FieldTypeMeta generateFieldTypeByTableSet(String tableName) {
    String setTypeCode = TableDictionary.generateSetType(tableName);
    return FieldTypeMeta.builder()
      .name(setTypeCode)
      .code(setTypeCode)
      .type(BasicDataType.FIELD_EMPTY)
      .tableType(tableName)
      .len(0)
      .javaType(Set.class.getName())
      .build();
  }

  public static FieldTypeMeta getTableFieldTypeOrGenerate(String tableName) {
    String key = TableDictionary.generateObjectType(tableName);
    return tableFieldTypeMap.computeIfAbsent(key, EntityMetadataParser::generateFieldTypeByTable);
  }

  public static FieldTypeMeta getTableSetFieldTypeOrGenerate(String tableName) {
    String key = TableDictionary.generateSetType(tableName);
    return tableFieldTypeMap.computeIfAbsent(key, EntityMetadataParser::generateFieldTypeByTableSet);
  }

  public static FieldTypeMeta getFieldTypeByType(Class<?> tClass) {
    if (Integer.class.equals(tClass) || Long.class.equals(tClass)) {
      return NUMBER_TYPE;
    } else if (String.class.equals(tClass)) {
      return TEXT_TYPE;
    } else if (LocalDateTime.class.equals(tClass)) {
      return DATETIME_TYPE;
    } else if (LocalDate.class.equals(tClass)) {
      return DATE_TYPE;
    } else if (Boolean.class.equals(tClass)) {
      return BOOLEAN_TYPE;
    } else if (BigDecimal.class.equals(tClass)) {
      return NUMBER_TYPE;
    } else if (tClass.isEnum()) {
      return SHORT_TEXT_TYPE;
    } else {
      return TEXT_TYPE;
    }
  }


  public static List<Field> getFields(Class<?> tClass) {
    List<Field> fieldList = new ArrayList<>();
    if (!Object.class.equals(tClass) && tClass != null) {
      fieldList.addAll(Arrays.asList(tClass.getDeclaredFields()));
      fieldList.addAll(getFields(tClass.getSuperclass()));
    }
    return fieldList;
  }


}
