package com.fast.core.data.utils;


import org.apache.commons.codec.digest.DigestUtils;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import static com.fast.core.data.Constants.*;

/**
 * @program: resource-shared
 * @description
 * @author: liunan
 * @create: 2019-04-17 09:39
 **/
public class TableDictionary {

    // 表表和字段表的 code
    public final static String TABLE_CODE = "ROOT_TABLE_CODE";
    public final static String FIELD_CODE = "ROOT_FIELD_CODE";

    // 关联类型
    public final static String RELATION = "RELATION";
    public final static String ONE_TO_ONE = "ONE_TO_ONE";
    public final static String ONE_TO_MANY = "ONE_TO_MANY";


    public final static String DATABASE_TYPE = "DATABASE_TYPE";

    // 字典CODE
    public final static String FIELD_FORMAT = "FIELD_FORMAT";

    public final static String DICT = "DICT";

    public final static String CODE = "CODE";

    public final static String UUID = "UUID";

    public final static String SHORT_TEXT = "SHORT_TEXT";

    public final static String DATETIME = "DATETIME";


    // empty 字段名称
    public final static String DEFAULT_EMPTY = "default_empty";

    public final static Map<String, String> defaultTypeCodeMap = new HashMap<>();

    static {
        defaultTypeCodeMap.put(ID, CODE);
        defaultTypeCodeMap.put(CREATE_USER_ID, CODE);
        defaultTypeCodeMap.put(CREATOR, SHORT_TEXT);
        defaultTypeCodeMap.put(CREATE_DATE, DATETIME);
        defaultTypeCodeMap.put(MODIFY_USER_ID, CODE);
        defaultTypeCodeMap.put(MODIFIER, SHORT_TEXT);
        defaultTypeCodeMap.put(MODIFY_DATE, DATETIME);
        defaultTypeCodeMap.put(BELONG_ID, CODE);
    }

    public static String generateObjectType(String tableName) {
        return "Object<" + tableName + ">";
    }

    public static String generateSetType(String tableName) {
        return "Set<" + tableName + ">";
    }

    public static boolean isObjectType(String typeCode) {
        return typeCode.startsWith("Object<");
    }

    public static boolean isSetType(String typeCode) {
        return typeCode.startsWith("Set<");
    }

    public static String generateFkName(String tableName, String fkTableName, String fieldName) {
        String allFkName = MessageFormat.format("fk_{0}_to_{1}_as_{2}", tableName, fkTableName, fieldName);
        return "fk" + DigestUtils.md5Hex(allFkName);
    }


    public static String getDefaultFieldTypeCode(String defaultField) {
        return defaultTypeCodeMap.get(defaultField);
    }

}
