package com.fast.core.rule;

/**
 * Created by l<PERSON><PERSON> on 17/1/5.
 * <p>
 * 多个不同的地方有权限则加起来
 */
public class GenericPurviewBase {

    public final static int NO_PRIVILEGE = 0;

    public final static int QUERY_OR_USE_PRIVILEGE = 1;// 查看权限

    public final static int CREATE_PRIVILEGE = 2;// 添加权限

    public final static int DELETE_PRIVILEGE = 4;// 删除权限

    public final static int UPDATE_PRIVILEGE = 8;// 更新权限

    public final static int ENCRYPT_PRIVILEGE = 16; //加密权限

    public final static int DECRYPT_PRIVILEGE = 32;// 解密权限

    public final static int SENSITIVE_PRIVILEGE = 64; //脱敏权限

    public final static int NO_SIGN_SENSITIVE_PRIVILEGE = 128;// 单条不脱敏权限

    public final static int NO_MANY_SENSITIVE_PRIVILEGE = 256;// 批量不脱敏权限


    public final static int ALL_PRIVILEGE = QUERY_OR_USE_PRIVILEGE
            | CREATE_PRIVILEGE
            | DELETE_PRIVILEGE
            | UPDATE_PRIVILEGE
            | DECRYPT_PRIVILEGE
            | NO_SIGN_SENSITIVE_PRIVILEGE
            | NO_MANY_SENSITIVE_PRIVILEGE;

    /**
     * 判断是否具有操作权限
     *
     * @param privilege
     * @return
     */
    public static boolean isValidPrivilege(int privilege) {

        if ((privilege & QUERY_OR_USE_PRIVILEGE) != 0) {
            return true;
        }
        if ((privilege & CREATE_PRIVILEGE) != 0) {
            return true;
        }
        if ((privilege & DELETE_PRIVILEGE) != 0) {
            return true;
        }
        if ((privilege & UPDATE_PRIVILEGE) != 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否具有查看和使用权限
     *
     * @param privilege
     * @return
     */
    public static boolean checkQueryOrUsePrivilege(int privilege) {
        return (privilege & QUERY_OR_USE_PRIVILEGE) != 0;
    }

    /**
     * 判断是否有添加权限
     *
     * @param privilege
     * @return
     */
    public static boolean checkCreatePrivilege(int privilege) {
        return (privilege & CREATE_PRIVILEGE) != 0;
    }

    /**
     * 判断是否有删除权限
     *
     * @param privilege
     * @return
     */
    public static boolean checkDeletePrivilege(int privilege) {
        return (privilege & DELETE_PRIVILEGE) != 0;
    }

    /**
     * 查是非有更新权限
     *
     * @param privilege
     * @return
     */
    public static boolean checkUpdatePrivilege(int privilege) {
        return (privilege & UPDATE_PRIVILEGE) != 0;
    }

    /**
     * 查看对字段是否加密
     *
     * @param privilege
     * @return
     */
    public static boolean checkEncryptPrivilege(int privilege) {
        return (privilege & ENCRYPT_PRIVILEGE) != 0;
    }


    /**
     * 查看是否有查看解密后数据
     *
     * @param privilege
     * @return
     */
    public static boolean checkDecryptPrivilege(int privilege) {
        return (privilege & DECRYPT_PRIVILEGE) != 0;
    }


    /**
     * 查看对字段是否脱敏
     *
     * @param privilege
     * @return
     */
    public static boolean checkSignSensitivePrivilege(int privilege) {
        return (privilege & SENSITIVE_PRIVILEGE) != 0;
    }


    /**
     * 查看是否有查看不脱敏权限
     *
     * @param privilege
     * @return
     */
    public static boolean checkNoSignSensitivePrivilege(int privilege) {
        return (privilege & NO_SIGN_SENSITIVE_PRIVILEGE) != 0;
    }

    /**
     * 查看是否有查看不脱敏权限
     *
     * @param privilege
     * @return
     */
    public static boolean checkNoManySensitivePrivilege(int privilege) {
        return (privilege & NO_MANY_SENSITIVE_PRIVILEGE) != 0;
    }


    public static boolean check(int privilege, int role) {
        return (privilege & role) != 0;
    }

    /**
     * 多个权限合并
     *
     * @param privileges
     * @return
     */
    public static int sumPrivilege(int... privileges) {
        int sumPrivilege = 0;
        for (int item : privileges) {
            if ((item & QUERY_OR_USE_PRIVILEGE) != 0 && (sumPrivilege & QUERY_OR_USE_PRIVILEGE) == 0) {
                sumPrivilege |= QUERY_OR_USE_PRIVILEGE;
            }
            if ((item & CREATE_PRIVILEGE) != 0 && (sumPrivilege & CREATE_PRIVILEGE) == 0) {
                sumPrivilege |= CREATE_PRIVILEGE;
            }
            if ((item & DELETE_PRIVILEGE) != 0 && (sumPrivilege & DELETE_PRIVILEGE) == 0) {
                sumPrivilege |= DELETE_PRIVILEGE;
            }
            if ((item & UPDATE_PRIVILEGE) != 0 && (sumPrivilege & UPDATE_PRIVILEGE) == 0) {
                sumPrivilege |= UPDATE_PRIVILEGE;
            }

            if ((item & DECRYPT_PRIVILEGE) != 0 && (sumPrivilege & DECRYPT_PRIVILEGE) == 0) {
                sumPrivilege |= DECRYPT_PRIVILEGE;
            }
            if ((item & NO_SIGN_SENSITIVE_PRIVILEGE) != 0 && (sumPrivilege & NO_SIGN_SENSITIVE_PRIVILEGE) == 0) {
                sumPrivilege |= NO_SIGN_SENSITIVE_PRIVILEGE;
            }
            if ((item & NO_MANY_SENSITIVE_PRIVILEGE) != 0 && (sumPrivilege & NO_MANY_SENSITIVE_PRIVILEGE) == 0) {
                sumPrivilege |= NO_MANY_SENSITIVE_PRIVILEGE;
            }

        }
        return sumPrivilege;
    }

    /**
     * 多个权限删除
     *
     * @param tagerPrivileges
     * @param sourcePrivileges
     * @return
     */
    public static int delPrivilege(int tagerPrivileges, int sourcePrivileges) {
        if ((sourcePrivileges & QUERY_OR_USE_PRIVILEGE) != 0 && (tagerPrivileges & QUERY_OR_USE_PRIVILEGE) != 0) {
            tagerPrivileges = tagerPrivileges & (~QUERY_OR_USE_PRIVILEGE);
        }
        if ((sourcePrivileges & CREATE_PRIVILEGE) != 0 && (tagerPrivileges & CREATE_PRIVILEGE) != 0) {
            tagerPrivileges = tagerPrivileges & (~CREATE_PRIVILEGE);
        }
        if ((sourcePrivileges & DELETE_PRIVILEGE) != 0 && (tagerPrivileges & DELETE_PRIVILEGE) != 0) {
            tagerPrivileges = tagerPrivileges & (~DELETE_PRIVILEGE);
        }
        if ((sourcePrivileges & UPDATE_PRIVILEGE) != 0 && (tagerPrivileges & UPDATE_PRIVILEGE) != 0) {
            tagerPrivileges = tagerPrivileges & (~UPDATE_PRIVILEGE);
        }
        if ((sourcePrivileges & DECRYPT_PRIVILEGE) != 0 && (tagerPrivileges & DECRYPT_PRIVILEGE) != 0) {
            tagerPrivileges = tagerPrivileges & (~DECRYPT_PRIVILEGE);
        }
        if ((sourcePrivileges & NO_SIGN_SENSITIVE_PRIVILEGE) != 0 && (tagerPrivileges & NO_SIGN_SENSITIVE_PRIVILEGE) != 0) {
            tagerPrivileges = tagerPrivileges & (~NO_SIGN_SENSITIVE_PRIVILEGE);
        }
        if ((sourcePrivileges & NO_MANY_SENSITIVE_PRIVILEGE) != 0 && (tagerPrivileges & NO_MANY_SENSITIVE_PRIVILEGE) != 0) {
            tagerPrivileges = tagerPrivileges & (~NO_MANY_SENSITIVE_PRIVILEGE);
        }
        return tagerPrivileges;
    }

}
