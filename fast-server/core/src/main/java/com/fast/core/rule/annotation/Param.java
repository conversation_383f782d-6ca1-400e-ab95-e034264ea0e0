package com.fast.core.rule.annotation;


import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * @program: resource-shared
 * @description
 * @author: liunan
 * @create: 2019-06-05 16:23
 **/

@Target({PARAMETER})
@Retention(RUNTIME)
@Documented
public @interface Param {

    String value();
}
