package com.fast.core.rule.table;

import com.fast.core.rule.StorageStatus;
import com.fast.core.rule.annotation.Param;

import java.util.Map;

/**
 * @program: resource-shared
 * @description
 * @author: liunan
 * @create: 2019-04-16 15:00
 **/
public interface TableSingleRowCheck {

    // storageStatus true 为插入  false 为更新
    Boolean handle(@Param("row") Map<String, Object> row, @Param("storageStatus") StorageStatus storageStatus);
}
