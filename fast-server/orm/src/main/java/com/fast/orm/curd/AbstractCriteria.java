package com.fast.orm.curd;

import com.fast.core.data.TableMeta;
import org.jooq.AttachableQueryPart;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.conf.ParamType;
import org.jooq.conf.Settings;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConfiguration;

import java.io.Serial;
import java.util.HashMap;
import java.util.Map;

public abstract class AbstractCriteria<T extends AttachableQueryPart> implements Criteria<T> {
  @Serial
  private static final long serialVersionUID = 1L;

  protected boolean compiled = false;
  protected boolean batchExecute = false;
  protected final DSLContext dsl;
  protected final TableFactory tableFactory;
  protected final TableMeta tableMeta;
  protected T criteria;


  public AbstractCriteria(TableMeta tableMeta) {
    this.tableFactory = TableFactory.generate(tableMeta.getDbName());
    this.tableMeta = tableMeta;
    DefaultConfiguration configuration = new DefaultConfiguration();
    configuration.setSQLDialect(SQLDialect.POSTGRES);
    Settings settings = new Settings()
      .withParamType(ParamType.NAMED)
      .withRenderNamedParamPrefix("#");
    // 禁用jooq小提示
    System.setProperty("org.jooq.no-logo", "true");
    configuration.setSettings(settings);
    this.dsl = DSL.using(configuration);
  }

  public Boolean isBatch() {
    return this.batchExecute;
  }

  public String getSql() {
    if (!this.compiled) {
      throw new RuntimeException("请先编译查询条件");
    }
    return this.criteria.getSQL(ParamType.NAMED);
  }

  public Map<String, Object> getParams() {
    if (!this.compiled) {
      throw new RuntimeException("请先编译查询条件");
    }
    if (this.batchExecute) {
      throw new RuntimeException("该操作是批量执行，请调用batchParams方法获取参数");
    }
    Map<String, Object> params = new HashMap<>();
    this.criteria.getParams().forEach((k, v) -> {
      params.put(k.substring(1, k.length() - 1), v.getValue());
    });
    return params;
  }


}
