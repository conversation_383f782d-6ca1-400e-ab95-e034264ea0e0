package com.fast.orm.curd;

import org.jooq.AttachableQueryPart;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface Criteria<T extends AttachableQueryPart> extends Serializable {

  void compile();

  Boolean isBatch();

  String getSql();

  Map<String, Object> getParams();

  default List<Map<String, Object>> getBatchParams() {
    throw new RuntimeException("暂不支持批量处理");
  }

}
