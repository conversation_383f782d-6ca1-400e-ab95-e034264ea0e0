package com.fast.orm.curd;

import com.fast.core.data.ConstraintMeta;
import com.fast.core.data.FieldMeta;
import com.fast.core.data.TableMeta;
import com.fast.orm.curd.condition.*;
import com.fast.orm.curd.condition.BaseCondition;
import com.fast.orm.curd.condition.BaseTypeCondition;
import jakarta.validation.ValidationException;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.fast.core.data.Constants.ID;
import static com.fast.orm.curd.JoinType.*;
import static org.jooq.impl.DSL.*;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2021/11/18 20:38
 **/
public class CriteriaUtils {

  public static Condition convertCondition(Table<Record> currentTable, TableMeta currentTableMeta, BaseTypeCondition whereCondition, TableFactory tableFactory) {
    Condition condition = DSL.noCondition();
    if (whereCondition.getFieldConditions() != null) {
      List<Condition> andConditions = whereCondition.getFieldConditions().entrySet().stream().map((entry) -> {
        if (entry.getValue() instanceof BaseTypeCondition) {
          // 关联表处理。
          return convertBastTypeCondition(entry.getKey(), currentTable, currentTableMeta, (BaseTypeCondition) entry.getValue(), tableFactory);
        } else {
          return convertBasicCondition(entry.getKey(), currentTable, currentTableMeta, entry.getValue(), tableFactory, CriteriaUtils::conditionField);
        }
      }).flatMap(Collection::stream).toList();
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (whereCondition.getAnd() != null) {
      List<Condition> andConditions = whereCondition.getAnd()
        .stream()
        .map(baseType -> convertCondition(currentTable, currentTableMeta, baseType, tableFactory))
        .collect(Collectors.toList());
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (whereCondition.getOr() != null) {
      List<Condition> orConditions = whereCondition.getOr()
        .stream()
        .map(baseType -> convertCondition(currentTable, currentTableMeta, baseType, tableFactory))
        .collect(Collectors.toList());
      if (!orConditions.isEmpty()) {
        condition = condition.and(DSL.or(orConditions));
      }
    }
    if (whereCondition.getNot() != null) {
      condition = condition.andNot(
        convertCondition(currentTable, currentTableMeta, whereCondition.getNot(), tableFactory));
    }
    if (whereCondition.getExist() != null) {
      List<Select<?>> selects = convertExistSelect(currentTable, currentTableMeta, whereCondition.getExist(), tableFactory);
      for (Select<?> select : selects) {
        condition = condition.andExists(select);
      }
    }
    return condition;
  }

  public static List<Condition> convertBastTypeCondition(String key, Table<Record> currentTable, TableMeta currentTableMeta, BaseTypeCondition value, TableFactory tableFactory) {
    List<Condition> resultCondition = new ArrayList<>();
    FieldMeta fieldMeta = currentTableMeta.getField(key);
    TableMeta joinTableMeta = currentTableMeta.getReactionTable(fieldMeta.getName());
    ConstraintMeta constraintMeta = currentTableMeta.getConstraint(fieldMeta.getName());
    if (constraintMeta == null) {
      throw new RuntimeException("没有找到关联字段：" + fieldMeta.getName());
    }
    // 判断子查询还是join,子查询不需要处理
    if (!JoinType.NONE.equals(constraintMeta.getJoinType())) { // join处理
      Table<Record> joinTable = tableFactory.getTableByTableFieldName(constraintMeta.getFieldName());
      if (!tableFactory.existJoinTableByTableFieldName(constraintMeta.getFieldName())) {
        joinTable = tableFactory.generateJoinTable(joinTableMeta.getDbName(), constraintMeta.getFieldName());
        // 添加了条件，则覆盖一下returnTable
        Table<Record> currentReturnTable = joinTable(tableFactory.getReturnTable(), currentTable, joinTable, constraintMeta);
        tableFactory.setReturnTable(currentReturnTable);
      }
      // 如果是join，则先join下，然后再处理field
      resultCondition.add(convertCondition(joinTable, joinTableMeta, value, tableFactory));
    } else { // 子查询处理
      resultCondition.add(relationSubQuery(constraintMeta, currentTable, currentTableMeta, value, tableFactory));
    }
    return resultCondition;
  }


  public static List<Condition> convertBasicCondition(String key, Table<Record> currentTable, TableMeta currentTableMeta, BaseCondition<?> baseCondition, TableFactory tableFactory, ConditionFiledHandle handle) {
    FieldMeta fieldMeta = currentTableMeta.getField(key);
    String fieldName = fieldMeta.getDbName();
    if (baseCondition instanceof StringCondition) {
      Field<String> field = FieldUtils.generateField(currentTable, fieldName, String.class);
      return ((StringCondition) baseCondition).convert(field, new BaseCondition.ConditionField<String>() {
        @Override
        public Field<String> handleMax(Boolean max) {
          throw new ValidationException("string 类型字段暂不支持max");
        }

        @Override
        public Field<String> handleField(String fieldPath) {
          // 递归处理字段，根据fieldPath
          return handle.conditionField(fieldPath, String.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<String> handleParam(String value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<String>> handleParam(List<String> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    } else if (baseCondition instanceof IdCondition) {
      Field<String> field = FieldUtils.generateField(currentTable, fieldName, String.class);
      return ((IdCondition) baseCondition).convert(field, new BaseCondition.ConditionField<String>() {
        @Override
        public Field<String> handleMax(Boolean max) {
          throw new ValidationException("string 类型字段暂不支持max");
        }

        @Override
        public Field<String> handleField(String fieldPath) {
          return handle.conditionField(fieldPath, String.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<String> handleParam(String value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<String>> handleParam(List<String> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    } else if (baseCondition instanceof BooleanCondition) {
      Field<Boolean> field = FieldUtils.generateField(currentTable, fieldName, Boolean.class);
      return ((BooleanCondition) baseCondition).convert(field, new BaseCondition.ConditionField<Boolean>() {
        @Override
        public Field<Boolean> handleMax(Boolean max) {
          throw new ValidationException("string 类型字段暂不支持max");
        }

        @Override
        public Field<Boolean> handleField(String fieldPath) {
          return handle.conditionField(fieldPath, Boolean.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<Boolean> handleParam(Boolean value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<Boolean>> handleParam(List<Boolean> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    } else if (baseCondition instanceof IntCondition) {
      Field<Integer> field = FieldUtils.generateField(currentTable, fieldName, Integer.class);
      return ((IntCondition) baseCondition).convert(field, new BaseCondition.ConditionField<Integer>() {
        @Override
        public Field<Integer> handleMax(Boolean max) {
          Table<Record> auditTable = table(currentTableMeta.getDbName()).as("audit");
          Field<String> auditFieldId = FieldUtils.generateField(auditTable, ID, String.class);
          Field<String> currentFieldId = FieldUtils.generateField(currentTable, ID, String.class);
          Field<Integer> currentField = FieldUtils.generateField(auditTable, key, Integer.class);
          if (Boolean.TRUE.equals(max)) {
            return select(coalesce(max(currentField), 0))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          } else {
            return select(coalesce(min(currentField), 0))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          }
        }

        @Override
        public Field<Integer> handleField(String fieldPath) {
          return handle.conditionField(fieldPath, Integer.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<Integer> handleParam(Integer value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<Integer>> handleParam(List<Integer> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    } else if (baseCondition instanceof BigDecimalCondition) {
      Field<BigDecimal> field = FieldUtils.generateField(currentTable, fieldName, BigDecimal.class);
      return ((BigDecimalCondition) baseCondition).convert(field, new BaseCondition.ConditionField<BigDecimal>() {
        @Override
        public Field<BigDecimal> handleMax(Boolean max) {
          Table<Record> auditTable = table(currentTableMeta.getDbName()).as("audit");
          Field<String> auditFieldId = FieldUtils.generateField(auditTable, ID, String.class);
          Field<String> currentFieldId = FieldUtils.generateField(currentTable, ID, String.class);
          Field<BigDecimal> currentField = FieldUtils.generateField(auditTable, key, BigDecimal.class);
          if (Boolean.TRUE.equals(max)) {
            return select(coalesce(max(currentField), 0))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          } else {
            return select(coalesce(min(currentField), 0))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          }
        }

        @Override
        public Field<BigDecimal> handleField(String fieldPath) {
          return handle.conditionField(fieldPath, BigDecimal.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<BigDecimal> handleParam(BigDecimal value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<BigDecimal>> handleParam(List<BigDecimal> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    } else if (baseCondition instanceof DateCondition) {
      Field<LocalDate> field = FieldUtils.generateField(currentTable, fieldName, LocalDate.class);
      return ((DateCondition) baseCondition).convert(field, new BaseCondition.ConditionField<LocalDate>() {
        @Override
        public Field<LocalDate> handleMax(Boolean max) {
          Table<Record> auditTable = table(currentTableMeta.getDbName()).as("audit");
          Field<String> auditFieldId = FieldUtils.generateField(auditTable, ID, String.class);
          Field<String> currentFieldId = FieldUtils.generateField(currentTable, ID, String.class);
          Field<LocalDate> currentField = FieldUtils.generateField(auditTable, key, LocalDate.class);
          if (Boolean.TRUE.equals(max)) {
            return select(coalesce(max(currentField), LocalDate.EPOCH))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          } else {
            return select(coalesce(min(currentField), LocalDate.EPOCH))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          }
        }

        @Override
        public Field<LocalDate> handleField(String fieldPath) {
          return handle.conditionField(fieldPath, LocalDate.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<LocalDate> handleParam(LocalDate value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<LocalDate>> handleParam(List<LocalDate> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    } else if (baseCondition instanceof DateTimeCondition) {
      Field<LocalDateTime> field = FieldUtils.generateField(currentTable, fieldName, LocalDateTime.class);
      return ((DateTimeCondition) baseCondition).convert(field, new BaseCondition.ConditionField<LocalDateTime>() {
        @Override
        public Field<LocalDateTime> handleMax(Boolean max) {
          Table<Record> auditTable = table(currentTableMeta.getDbName()).as("audit");
          Field<String> auditFieldId = FieldUtils.generateField(auditTable, ID, String.class);
          Field<String> currentFieldId = FieldUtils.generateField(currentTable, ID, String.class);
          Field<LocalDateTime> currentField = FieldUtils.generateField(auditTable, key, LocalDateTime.class);
          if (Boolean.TRUE.equals(max)) {
            return select(coalesce(max(currentField), LocalDate.EPOCH.atStartOfDay()))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          } else {
            return select(coalesce(min(currentField), LocalDate.EPOCH.atStartOfDay()))
              .from(auditTable)
              .where(auditFieldId.eq(currentFieldId))
              .asField();
          }
        }

        @Override
        public Field<LocalDateTime> handleField(String fieldPath) {
          return handle.conditionField(fieldPath, LocalDateTime.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<LocalDateTime> handleParam(LocalDateTime value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<LocalDateTime>> handleParam(List<LocalDateTime> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    } else if (baseCondition instanceof ObjectCondition) {
      Field<Object> field = FieldUtils.generateField(currentTable, fieldName, Object.class);
      return ((ObjectCondition) baseCondition).convert(field, new BaseCondition.ConditionField<Object>() {
        @Override
        public Field<Object> handleMax(Boolean max) {
          throw new ValidationException("object 类型字段暂不支持max");
        }

        @Override
        public Field<Object> handleField(String fieldPath) {
          return handle.conditionField(fieldPath, Object.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<Object> handleParam(Object value) {
          return tableFactory.generateParam(key,value);
        }

        @Override
        public List<Param<Object>> handleParam(List<Object> value) {
          return value.stream().map(v -> tableFactory.generateParam(key,v)).toList();
        }
      });
    }
    return new ArrayList<>();
  }


  public static Table<Record> joinTable(Table<Record> returnTable, Table<Record> table, Table<Record> joinTable, ConstraintMeta constraintMeta) {
    TableOnStep<Record> tableOnStep = null;
    if (LEFT_OUTER_JOIN.equals(constraintMeta.getJoinType())) {
      tableOnStep = returnTable.leftJoin(joinTable);
    } else if (RIGHT_OUTER_JOIN.equals(constraintMeta.getJoinType())) {
      tableOnStep = returnTable.rightJoin(joinTable);
    } else if (FULL_JOIN.equals(constraintMeta.getJoinType())) {
      tableOnStep = returnTable.fullJoin(joinTable);
    } else if (INNER_JOIN.equals(constraintMeta.getJoinType())) {
      tableOnStep = returnTable.innerJoin(joinTable);
    } else {
      throw new RuntimeException("不支持的关联类型：" + constraintMeta.getJoinType());
    }
    // todo 验证table权限，添加table的自定义范围条件。
    List<Condition> joinCondition = constraintMeta.getConsColumns().stream().map(consColumnsVo -> {
      Field<Object> field = FieldUtils.generateField(table, consColumnsVo.getDbField());
      Field<Object> fkField = FieldUtils.generateField(joinTable, consColumnsVo.getDbFkField());
      return field.eq(fkField);
    }).toList();
    return tableOnStep.on(joinCondition.toArray(new Condition[0]));
  }


  public static <T> Field<T> conditionField(String fieldPath, Class<T> clazz, Table<Record> currentTable, TableMeta currentTableMeta, TableFactory tableFactory) {
    if (!fieldPath.contains(".")) { // 不带点，单表处理
      FieldMeta currentFieldMeta = currentTableMeta.getField(fieldPath);
      return FieldUtils.generateField(currentTable, currentFieldMeta.getDbName(), clazz);
    } else { // 带点则先关联再处理
      String[] fields = fieldPath.split("\\.");
      String selectName = fields[0];
      FieldMeta currentFieldMeta = currentTableMeta.getField(selectName);
      ConstraintMeta constraintMeta = currentTableMeta.getConstraint(currentFieldMeta.getName());
      if (constraintMeta == null) {
        throw new RuntimeException("没有找到关联字段：" + currentFieldMeta.getName());
      }
      // 判断子查询还是join,子查询不需要处理
      if (!JoinType.NONE.equals(constraintMeta.getJoinType())) {
        TableMeta joinTableMeta = currentTableMeta.getReactionTable(constraintMeta.getFieldName());
        Table<Record> joinTable = tableFactory.getTableByTableFieldName(constraintMeta.getFieldName());
        if (!tableFactory.existJoinTableByTableFieldName(constraintMeta.getFieldName())) {
          joinTable = tableFactory.generateJoinTable(joinTableMeta.getDbName(), constraintMeta.getFieldName());
          // 添加了条件，则覆盖一下table
          Table<Record> currentReturnTable = joinTable(tableFactory.getReturnTable(), currentTable, joinTable, constraintMeta);
          tableFactory.setReturnTable(currentReturnTable);
        }
        String[] currentFields = Arrays.stream(fields).skip(1).toArray(String[]::new);
        return conditionField(String.join(".", currentFields), clazz, joinTable, joinTableMeta, tableFactory);
      } else {
        throw new RuntimeException("子查询类型字段,不能通过这样通过条件来关联：" + currentFieldMeta.getName());
      }
    }
  }


  public static Condition relationSubQuery(ConstraintMeta constraintMeta, Table<Record> currentTable, TableMeta currentTableMeta, BaseTypeCondition value, TableFactory tableFactory) {
    List<org.jooq.SelectField<?>> relationFields = new ArrayList<>();
    List<org.jooq.SelectField<?>> subTableFields = new ArrayList<>();
    TableMeta subTableMeta = currentTableMeta.getReactionTable(constraintMeta.getFieldName());
    TableFactory subTableFactory = TableFactory.generate(tableFactory, subTableMeta.getDbName());
    Table<Record> subTable = subTableFactory.getMainTable();
    constraintMeta.getConsColumns().forEach(consColumnsVo -> {
      org.jooq.SelectField<?> relationField = FieldUtils.generateField(currentTable, consColumnsVo.getDbField());
      relationFields.add(relationField);
      org.jooq.SelectField<?> subTableField = FieldUtils.generateField(subTable, consColumnsVo.getDbFkField());
      subTableFields.add(subTableField);
    });
    Condition subQueryCondition = convertCondition(subTable, subTableMeta, value, subTableFactory);
    // todo 验证table权限，添加table的自定义范围条件。
    Select<?> select = select(subTableFields).from(subTableFactory.getReturnTable()).where(subQueryCondition);
    return row(relationFields.toArray()).in(select);
  }

  public static List<Select<?>> convertExistSelect(Table<Record> currentTable, TableMeta currentTableMeta, BaseTypeCondition searchCondition, TableFactory tableFactory) {
    if (searchCondition.getFieldConditions() != null) {
      return searchCondition.getFieldConditions().entrySet().stream().map((entry) -> {
        FieldMeta fieldMeta = currentTableMeta.getField(entry.getKey());
        TableMeta subTableMeta = currentTableMeta.getReactionTable(fieldMeta.getName());
        ConstraintMeta constraintMeta = currentTableMeta.getConstraint(fieldMeta.getName());
        TableFactory subTableFactory = TableFactory.generate(tableFactory, subTableMeta.getDbName());
        Table<Record> subTable = subTableFactory.getMainTable();
        Field<Object> existField = FieldUtils.generateField(subTable, ID);
        List<Condition> relationConditions = new ArrayList<>();
        constraintMeta.getConsColumns().forEach(consColumnsVo -> {
          Field<Object> relationField = FieldUtils.generateField(currentTable, consColumnsVo.getDbField());
          Field<Object> subTableField = FieldUtils.generateField(subTable, consColumnsVo.getDbFkField());
          relationConditions.add(relationField.eq(subTableField));
        });
        Condition subQueryCondition = convertCondition(subTable, subTableMeta, (BaseTypeCondition) entry.getValue(), subTableFactory);
        relationConditions.add(subQueryCondition);
        // todo 验证table权限，添加table的自定义范围条件。
        return (Select<?>) select(existField).from(subTableFactory.getReturnTable())
          .where(relationConditions);
      }).collect(Collectors.toList());
    } else {
      return new ArrayList<>();
    }
  }


}
