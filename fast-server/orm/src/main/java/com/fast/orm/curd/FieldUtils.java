package com.fast.orm.curd;

import com.fast.core.data.type.BasicDataType;
import org.jooq.DataType;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Table;
import org.jooq.impl.SQLDataType;

import static org.jooq.impl.DSL.field;

/**
 * @program: project-management-git
 * @description
 * @author: liunan
 * @create: 2022/3/15 15:51
 **/
public class FieldUtils {

    public static DataType<?> getDataType(BasicDataType basicDataType) {
        switch (basicDataType) {
            case FIELD_BOOLEAN:
                return SQLDataType.BOOLEAN;
            case FIELD_INTEGER:
                return SQLDataType.INTEGER;
            case FIELD_BIG_DECIMAL:
                return SQLDataType.NUMERIC;
            case FIELD_LOB:
                return SQLDataType.CLOB;
            case FIELD_DATE:
                return SQLDataType.LOCALDATE;
            case FIELD_DATE_TIME:
                return SQLDataType.LOCALDATETIME;
            case FIELD_JSON:
                return SQLDataType.JSONB;
            default:
                return SQLDataType.VARCHAR;
        }
    }

  public static <T> Field<T> generateField(Table<Record> table, String fieldName, Class<T> tClass) {
    return field( table.getName() + "." + fieldName, tClass);
  }

  public static Field<Object> generateField(Table<Record> table, String fieldName) {
    return generateField(table, fieldName, Object.class);
  }

  public static <T> Field<T> generateField(Table<Record> table, String fieldName, DataType<T> dataType) {
    return field(table.getName() + "." + fieldName, dataType);
  }



}
