package com.fast.orm.curd;

import com.fast.tools.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import org.jooq.Field;
import org.jooq.Param;
import org.jooq.Record;
import org.jooq.Table;
import org.jooq.impl.DSL;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * @program: project-management
 * @description 生成jooq使用的table。记录sql中的所有table（包含join的table）和最终return的table（关联之后return的tale则会改变）。
 * @author: liunan
 * @create: 2021/11/16 9:23
 **/
public class TableFactory {

  public TableFactory(String tableName) {
    this.generateTable(tableName);
  }

  public TableFactory(TableFactory tableFactory, String tableName) {
    this.parent = tableFactory;
    this.prefixAlias = tableFactory.getCurrentAlias() == null ? "" : tableFactory.getCurrentAlias();
    this.generateTable(tableName);
  }


  private Map<String, Integer> paramIndexMap = new HashMap<>();

  /**
   * 如果有子查询则会有父
   */
  @Getter
  private TableFactory parent;

  private final String[] ALIAS_ALL = {"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p",
    "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};

  private String currentAlias;

  private String prefixAlias = "";

  @Getter
  private Table<Record> mainTable;
  @Getter
  private String mainTableName;
  /**
   * joinTable的记录
   */
  private final ConcurrentMap<String, Table<Record>> aliasJoinTableMap = new ConcurrentHashMap<>();

  /**
   * join之后则需要随时更新，目前需要手动记录。
   */
  @Getter
  @Setter
  private Table<Record> returnTable;

  /**
   * 标记查询是group
   */
  @Getter
  @Setter
  private Boolean group = false;
  /**
   * 记录需要group的字段
   */
  @Getter
  private List<Field<?>> groupFields = new ArrayList<>();


  private String getCurrentAlias() {
    return this.currentAlias;
  }

  private String getAlias() {
    String alias = null;
    if (StringUtils.isNotBlank(this.currentAlias)) {
      int index = Arrays.asList(ALIAS_ALL).indexOf(currentAlias);
      alias = ALIAS_ALL[index + 1];
    } else {
      alias = ALIAS_ALL[0];
    }
    this.currentAlias = alias;
    return prefixAlias + alias;
  }

  private void generateTable(String tableName) {
    this.mainTable = DSL.table(tableName).as(getAlias());
    this.mainTableName = tableName;
    this.returnTable = this.mainTable;
  }

  public Table<Record> generateJoinTable(String joinTableDBName, String tableFieldName) {
    Table<Record> table = DSL.table(joinTableDBName).as(tableFieldName);
    aliasJoinTableMap.put(tableFieldName, table);
    return table;
  }

  public boolean existJoinTableByTableFieldName(String tableFieldName) {
    return this.aliasJoinTableMap.containsKey(tableFieldName);
  }

  public Table<Record> getTableByTableFieldName(String tableFieldName) {
    return this.aliasJoinTableMap.get(tableFieldName);
  }


  public String getMainTableAlias() {
    return this.mainTable.getName();
  }


  public <T> Param<T> generateParam(String key, T value) {
    int index = paramIndexMap.getOrDefault(key, 0);
    paramIndexMap.put(key, index + 1);
    return DSL.param("{" + this.prefixAlias + key + index + "}", value);
  }

  public static TableFactory generate(String tableName) {
    return new TableFactory(tableName);
  }

  public static TableFactory generate(TableFactory tableFactory, String tableName) {
    return new TableFactory(tableFactory, tableName);
  }


}
