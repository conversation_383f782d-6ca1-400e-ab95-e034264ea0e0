package com.fast.orm.curd.condition;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Param;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2021/11/15 14:54
 **/
public interface BaseCondition<T> extends Serializable {

  //    List<Condition> convert(TableVo tableVo, Table<Record> table, Field<T> field, String fieldName, Criteria.CriteriaBuilder queryBuilder, TableGenerate tableGenerate);
  List<Condition> convert(Field<T> field, ConditionField<T> conditionField);

  Map<String, Object> toMap();


  public static interface ConditionField<T> {
    /**
     * max条件时，字段处理
     *
     * @param max
     */
    public Field<T> handleMax(Boolean max);

    /**
     * 字段比较的条件时字段的处理。
     *
     * @param fieldPath 字段路径，user.name/user.org.name
     */
    public Field<T> handleField(String fieldPath);

    /**
     * 转换成参数
     *
     * @param value
     * @return
     */
    public Param<T> handleParam(T value);

    /**
     * 转换参数
     *
     * @param value
     * @return
     */
    public List<Param<T>> handleParam(List<T> value);
  }
}



