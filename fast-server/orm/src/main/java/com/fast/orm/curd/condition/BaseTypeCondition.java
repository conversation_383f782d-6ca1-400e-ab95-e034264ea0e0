package com.fast.orm.curd.condition;

import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.jooq.Condition;
import org.jooq.Field;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2021/11/15 14:46
 **/
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseTypeCondition implements BaseCondition<Object> {

  @Singular("and")
  private List<BaseTypeCondition> and;

  @Singular("or")
  private List<BaseTypeCondition> or;

  private BaseTypeCondition not;

  private BaseTypeCondition exist;

  @Singular("fieldCondition")
  private Map<String, BaseCondition<?>> fieldConditions;


  public BaseTypeCondition merge(BaseTypeCondition source) {
    BaseTypeCondition result = new BaseTypeCondition();
    if (source == null) {
      return null;
    }
    if (result.and == null) {
      result.and = new ArrayList<>();
    }
    if (source.getAnd() != null) {
      result.and.addAll(source.getAnd());
    }

    if (result.or == null) {
      result.or = new ArrayList<>();
    }
    if (source.getOr() != null) {
      result.or.addAll(source.getOr());
    }

    // fieldConditions 相同属性 合并。
    Map<String, List<BaseCondition<?>>> mapFieldConditions = new HashMap<>();
    if (this.fieldConditions != null) {
      this.fieldConditions.forEach((key, value) -> {
        mapFieldConditions.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
      });
    }
    if (source.fieldConditions != null) {
      source.fieldConditions.forEach((key, value) -> {
        mapFieldConditions.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
      });
    }
    // 清空fieldConditions
    if (result.fieldConditions == null) {
      result.fieldConditions = new HashMap<>();
    } else {
      result.fieldConditions.clear();
    }
    mapFieldConditions.forEach((key, value) -> {
      if (value.size() == 1) {
        result.fieldConditions.put(key, value.get(0));
      } else {
        value.forEach(item -> {
          if (result.and == null) {
            result.and = new ArrayList<>();
          }
          result.and.add(BaseTypeCondition.builder().fieldCondition(key, item).build());
        });
      }
    });
    // 多个not 就创建一个 and 链接起来
    if (this.not == null && source.getNot() != null) {
      result.not = source.getNot();
    } else if (this.not != null && source.getNot() != null) {
      if (result.and == null) {
        result.and = new ArrayList<>();
      }
      result.and.add(BaseTypeCondition.builder().and(this.not).and(source.getNot()).build());
      // 清空not
      result.not = null;
    }
    // 多个exist 就创建一个 and 链接起来
    if (this.exist == null && source.getExist() != null) {
      result.exist = source.getExist();
    } else if (this.exist != null && source.getExist() != null) {
      if (result.and == null) {
        result.and = new ArrayList<>();
      }
      result.and.add(BaseTypeCondition.builder().and(this.exist).and(source.getExist()).build());
      // 清空not
      result.exist = null;
    }
    return result;
  }


  @Override
  public List<Condition> convert(Field<Object> field, ConditionField<Object> conditionField) {
    throw new RuntimeException("baseTypeCondition 条件不能通过此处处理。");
  }

  @Override
  public Map<String, Object> toMap() {
    Map<String, Object> resultMap = new HashMap<>();
    if (ObjectUtils.isNotEmpty(this.getAnd())) {
      List<Map<String, Object>> andMaps = this.getAnd().stream().map(BaseTypeCondition::toMap).collect(Collectors.toList());
      resultMap.put("_and", andMaps);
    }
    if (ObjectUtils.isNotEmpty(this.getOr())) {
      List<Map<String, Object>> orMaps = this.getOr().stream().map(BaseTypeCondition::toMap).collect(Collectors.toList());
      resultMap.put("_or", orMaps);
    }
    if (ObjectUtils.isNotEmpty(this.getNot())) {
      resultMap.put("_not", this.getNot().toMap());
    }
    if (ObjectUtils.isNotEmpty(this.getExist())) {
      resultMap.put("_exist", this.getExist().toMap());
    }
    if (ObjectUtils.isNotEmpty(this.getFieldConditions())) {
      this.getFieldConditions().forEach((key, item) -> resultMap.put(key, item.toMap()));
    }
    return resultMap;
  }


  public BaseTypeCondition and(BaseTypeCondition baseTypeCondition) {
    this.merge(baseTypeCondition);
    return this;
  }

  public BaseTypeCondition or(BaseTypeCondition baseTypeCondition) {
    this.merge(baseTypeCondition);
    return this;
  }

  public BaseTypeCondition not(BaseTypeCondition baseTypeCondition) {
    this.merge(baseTypeCondition);
    return this;
  }

  public BaseTypeCondition exist(BaseTypeCondition baseTypeCondition) {
    this.merge(baseTypeCondition);
    return this;
  }

}
