package com.fast.orm.curd.condition;

import com.fast.tools.utils.StringUtils;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.jooq.Condition;
import org.jooq.Field;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fast.tools.utils.FastObjectUtils.cast;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2021/11/15 14:45
 **/
@Data
@Accessors(chain = true)
@Builder
public class BooleanCondition implements BaseCondition<Boolean> {
    private String feq;
    private String fneq;
    private Boolean eq;
    private Boolean neq;
    private Boolean isNull;

    public static BooleanCondition createCondition(Object params) {
        BooleanConditionBuilder builder = BooleanCondition.builder();
        if (params instanceof Map) {
            Map<String, Object> paramMap = cast(params);
            paramMap.forEach((key, value) -> {
                switch (key) {
                    case "_eq":
                        builder.eq((Boolean) value);
                        break;
                    case "_neq":
                        builder.neq((Boolean) value);
                        break;
                    case "_isNull":
                        builder.isNull((Boolean) value);
                        break;
                    case "_feq":
                        builder.feq((String) value);
                        break;
                    case "_fneq":
                        builder.fneq((String) value);
                        break;
                }
            });
        }
        return builder.build();
    }

    @Override
    public List<Condition> convert(Field<Boolean> field, ConditionField<Boolean> conditionField) {
        List<Condition> conditions = new ArrayList<>();
        if (eq != null) {
            conditions.add(field.eq(conditionField.handleParam(eq)));
        }
        if (neq != null) {
            conditions.add(field.ne(conditionField.handleParam(neq)));
        }
        if (isNull != null) {
            if (isNull) {
                conditions.add(field.isNull());
            } else {
                conditions.add(field.isNotNull());
            }
        }
        if (StringUtils.isNotBlank(feq)) {
            conditions.add(field.eq(conditionField.handleField(this.feq)));
        }
        if (StringUtils.isNotBlank(fneq)) {
            conditions.add(field.ne(conditionField.handleField(this.fneq)));
        }
        return conditions;
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> resultMap = new HashMap<>();
        if (ObjectUtils.isNotEmpty(this.getEq())) {
            resultMap.put("_eq", this.getEq());
        }
        if (ObjectUtils.isNotEmpty(this.getNeq())) {
            resultMap.put("_neq", this.getNeq());
        }
        if (this.getIsNull() != null) {
            resultMap.put("_isNull", this.getIsNull());
        }
        if (StringUtils.isNotEmpty(this.getFeq())) {
            resultMap.put("_feq", this.getFeq());
        }
        if (StringUtils.isNotEmpty(this.getFneq())) {
            resultMap.put("_fneq", this.getFneq());
        }
        return resultMap;
    }
}
