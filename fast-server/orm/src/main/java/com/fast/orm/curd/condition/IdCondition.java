package com.fast.orm.curd.condition;

import com.fast.tools.utils.FastObjectUtils;
import com.fast.tools.utils.StringUtils;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.jooq.Condition;
import org.jooq.Field;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fast.tools.utils.FastObjectUtils.cast;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2021/11/15 14:45
 **/
@Data
@Accessors(chain = true)
@Builder
public class IdCondition implements BaseCondition<String> {
    private String feq;
    private String fneq;
    private String eq;
    private String neq;
    private Boolean isNull;
    private List<String> in;
    private List<String> nin;

    public static IdCondition createCondition(Object params) {
        IdConditionBuilder builder = IdCondition.builder();
        if (params instanceof Map) {
            Map<String, Object> paramMap = cast(params);
            paramMap.forEach((key, value) -> {
                switch (key) {
                    case "_eq":
                        builder.eq((String) value);
                        break;
                    case "_neq":
                        builder.neq((String) value);
                        break;
                    case "_isNull":
                        builder.isNull((Boolean) value);
                        break;
                    case "_in":
                        builder.in((List<String>) value);
                        break;
                    case "_nin":
                        builder.nin((List<String>) value);
                        break;
                    case "_feq":
                        builder.feq((String) value);
                        break;
                    case "_fneq":
                        builder.fneq((String) value);
                        break;
                }
            });
        }
        return builder.build();
    }

    @Override
    public List<Condition> convert(Field<String> field, ConditionField<String> conditionField) {
        List<Condition> conditions = new ArrayList<>();
        if (eq != null) {
            conditions.add(field.eq(conditionField.handleParam(eq)));
        }
        if (neq != null) {
            conditions.add(field.ne(conditionField.handleParam(neq)));
        }
        if (isNull != null) {
            if (isNull) {
                conditions.add(field.isNull());
            } else {
                conditions.add(field.isNotNull());
            }
        }
        if (in != null) {
            conditions.add(field.in(conditionField.handleParam(in)));
        }
        if (nin != null) {
            conditions.add(field.notIn(conditionField.handleParam(nin)));
        }
        if (StringUtils.isNotBlank(feq)) {
            conditions.add(field.eq(conditionField.handleField(this.feq)));
        }
        if (StringUtils.isNotBlank(fneq)) {
            conditions.add(field.ne(conditionField.handleField(this.fneq)));
        }
        return conditions;
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> resultMap = new HashMap<>();
        if (ObjectUtils.isNotEmpty(this.getEq())) {
            resultMap.put("_eq", this.getEq());
        }
        if (ObjectUtils.isNotEmpty(this.getNeq())) {
            resultMap.put("_neq", this.getNeq());
        }
        if (this.getIsNull() != null) {
            resultMap.put("_isNull", this.getIsNull());
        }
        if (this.getIn() != null) {
            resultMap.put("_in", this.getIn());
        }
        if (this.getNin() != null) {
            resultMap.put("_nin", this.getNin());
        }
        if (StringUtils.isNotEmpty(this.getFeq())) {
            resultMap.put("_feq", this.getFeq());
        }
        if (StringUtils.isNotEmpty(this.getFneq())) {
            resultMap.put("_fneq", this.getFneq());
        }
        return resultMap;
    }
}
