package com.fast.orm.curd.condition;

import com.fast.tools.utils.FastObjectUtils;
import com.fast.tools.utils.StringUtils;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.jooq.Condition;
import org.jooq.Field;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fast.tools.utils.FastObjectUtils.cast;
import static org.jooq.impl.DSL.coalesce;


/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2021/11/15 14:45
 **/
@Data
@Accessors(chain = true)
@Builder
public class IntCondition implements BaseCondition<Integer> {
    private String feq;
    private String fneq;
    private String fgt;
    private String fgte;
    private String flt;
    private String flte;
    private Integer eq;
    private Integer neq;
    private Integer gt;
    private Integer gte;
    private Integer lt;
    private Integer lte;
    private List<Integer> between;
    private Boolean isNull;
    private List<Integer> in;
    private List<Integer> nin;
    private Boolean max;

    @Override
    public List<Condition> convert(Field<Integer> field, ConditionField<Integer> conditionField) {
        List<Condition> conditions = new ArrayList<>();
        if (eq != null) {
            conditions.add(field.eq(conditionField.handleParam(eq)));
        }
        if (neq != null) {
            conditions.add(field.ne(conditionField.handleParam(neq)));
        }
        if (gt != null) {
            conditions.add(field.gt(conditionField.handleParam(gt)));
        }
        if (gte != null) {
            conditions.add(field.ge(conditionField.handleParam(gte)));
        }
        if (lt != null) {
            conditions.add(field.lt(conditionField.handleParam(lt)));
        }
        if (lte != null) {
            conditions.add(field.le(conditionField.handleParam(lte)));
        }
        if (between != null) {
            if (between.size() > 1) {
                conditions.add(field.between(conditionField.handleParam(between.get(0)), conditionField.handleParam(between.get(1))));
            }
        }
        if (isNull != null) {
            if (isNull) {
                conditions.add(field.isNull());
            } else {
                conditions.add(field.isNotNull());
            }
        }
        if (in != null) {
            conditions.add(field.in(conditionField.handleParam(in)));
        }
        if (nin != null) {
            conditions.add(field.notIn(conditionField.handleParam(nin)));
        }
         if (max != null) {
            conditions.add(coalesce(field, 0).eq(conditionField.handleMax(this.max)));
        }

        if (StringUtils.isNotBlank(feq)) {
            conditions.add(field.eq(conditionField.handleField(this.feq)));
        }
        if (StringUtils.isNotBlank(fneq)) {
            conditions.add(field.ne(conditionField.handleField(this.fneq)));
        }
        if (StringUtils.isNotBlank(fgt)) {
            conditions.add(field.gt(conditionField.handleField(this.fgt)));
        }
        if (StringUtils.isNotBlank(fgte)) {
            conditions.add(field.ge(conditionField.handleField(this.fgte)));
        }
        if (StringUtils.isNotBlank(flt)) {
            conditions.add(field.lt(conditionField.handleField(this.flt)));
        }
        if (StringUtils.isNotBlank(flte)) {
            conditions.add(field.le(conditionField.handleField(this.flte)));
        }
        return conditions;
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> resultMap = new HashMap<>();
        if (ObjectUtils.isNotEmpty(this.getEq())) {
            resultMap.put("_eq", this.getEq());
        }
        if (ObjectUtils.isNotEmpty(this.getNeq())) {
            resultMap.put("_neq", this.getNeq());
        }
        if (ObjectUtils.isNotEmpty(this.getGt())) {
            resultMap.put("_gt", this.getGt());
        }
        if (ObjectUtils.isNotEmpty(this.getGte())) {
            resultMap.put("_gte", this.getGte());
        }
        if (ObjectUtils.isNotEmpty(this.getLt())) {
            resultMap.put("_lt", this.getLt());
        }
        if (ObjectUtils.isNotEmpty(this.getLte())) {
            resultMap.put("_lte", this.getLte());
        }
        if (this.getIsNull() != null) {
            resultMap.put("_isNull", this.getIsNull());
        }
        if (this.getIn() != null) {
            resultMap.put("_in", this.getIn());
        }
        if (this.getNin() != null) {
            resultMap.put("_nin", this.getNin());
        }
        if(ObjectUtils.isNotEmpty(this.getMax())){
            resultMap.put("_max", this.getMax());
        }
        if (StringUtils.isNotEmpty(this.getFeq())) {
            resultMap.put("_feq", this.getFeq());
        }
        if (StringUtils.isNotEmpty(this.getFneq())) {
            resultMap.put("_fneq", this.getFneq());
        }
        if (StringUtils.isNotEmpty(this.getFgt())) {
            resultMap.put("_fgt", this.getFgt());
        }
        if (StringUtils.isNotEmpty(this.getFgte())) {
            resultMap.put("_fgte", this.getFgte());
        }
        if (StringUtils.isNotEmpty(this.getFlt())) {
            resultMap.put("_flt", this.getFlt());
        }
        if (StringUtils.isNotEmpty(this.getFlte())) {
            resultMap.put("_flte", this.getFlte());
        }
        return resultMap;
    }

    public static IntCondition createCondition(Object params) {
        IntConditionBuilder builder = IntCondition.builder();
        if (params instanceof Map) {
            Map<String, Object> paramMap = cast(params);
            paramMap.forEach((key, value) -> {
                switch (key) {
                    case "_eq":
                        builder.eq((Integer) value);
                        break;
                    case "_neq":
                        builder.neq((Integer) value);
                        break;
                    case "_gt":
                        builder.gt((Integer) value);
                        break;
                    case "_gte":
                        builder.gte((Integer) value);
                        break;
                    case "_lt":
                        builder.lt((Integer) value);
                        break;
                    case "_lte":
                        builder.lte((Integer) value);
                        break;
                    case "_between":
                        builder.between((List<Integer>) value);
                        break;
                    case "_isNull":
                        builder.isNull((Boolean) value);
                        break;
                    case "_in":
                        builder.in((List<Integer>) value);
                        break;
                    case "_nin":
                        builder.nin((List<Integer>) value);
                        break;
                    case "_max":
                        builder.max((Boolean) value);
                        break;
                    case "_feq":
                        builder.feq((String) value);
                        break;
                    case "_fneq":
                        builder.fneq((String) value);
                        break;
                    case "_fgt":
                        builder.fgt((String) value);
                        break;
                    case "_fgte":
                        builder.fgte((String) value);
                        break;
                    case "_flt":
                        builder.flt((String) value);
                        break;
                    case "_flte":
                        builder.flte((String) value);
                        break;
                }
            });
        }
        return builder.build();
    }

}
