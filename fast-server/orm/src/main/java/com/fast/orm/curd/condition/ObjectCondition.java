package com.fast.orm.curd.condition;

import com.fast.tools.utils.StringUtils;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jooq.Condition;
import org.jooq.Field;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fast.tools.utils.FastObjectUtils.cast;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2021/11/15 14:45
 **/
@Data
@Accessors(chain = true)
@Builder
public class ObjectCondition implements BaseCondition<Object> {
    private String feq;
    private Object eq;
    private Boolean isNull;

    public static ObjectCondition createCondition(Object params) {
        ObjectConditionBuilder builder = ObjectCondition.builder();
        if (params instanceof Map) {
            Map<String, Object> paramMap = cast(params);
            paramMap.forEach((key, value) -> {
                switch (key) {
                    case "_eq":
                        builder.eq(value);
                        break;
                    case "_isNull":
                        builder.isNull((Boolean) value);
                        break;
                    case "_feq":
                        builder.feq((String) value);
                        break;
                }
            });
        }
        return builder.build();
    }

    @Override
    public List<Condition> convert(Field<Object> field, ConditionField<Object> conditionField) {
        List<Condition> conditions = new ArrayList<>();
        if (eq != null) {
            conditions.add(field.eq(conditionField.handleParam(eq)));
        }
        if (isNull != null) {
            if (isNull) {
                conditions.add(field.isNull());
            } else {
                conditions.add(field.isNotNull());
            }
        }
        if (StringUtils.isNotBlank(feq)) {
            conditions.add(field.eq(conditionField.handleField(feq)));
        }
        return conditions;
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> resultMap = new HashMap<>();
        if (this.getEq() != null) {
            resultMap.put("_eq", this.getEq());
        }
        if (this.getIsNull() != null) {
            resultMap.put("_isNull", this.getIsNull());
        }
        if (StringUtils.isNotEmpty(this.getFeq())) {
            resultMap.put("_feq", this.getFeq());
        }
        return resultMap;
    }
}
