package com.fast.orm.curd.field;

import com.fast.orm.curd.AggregateType;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import static com.fast.tools.utils.FastObjectUtils.cast;

@Data
@Accessors(chain = true)
@Builder
public class AggregateField implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  private String name;
  private AggregateType fn;
  private Boolean distinct;
  private AggregateField child;

  /**
   * 判断是否是最后一层
   */
  public Boolean isEnd() {
    return child == null;
  }


  public static AggregateField createAggregateField(Object params) {
    if (params instanceof Map) {
      Map<String, Object> paramMap = cast(params);
      List<String> keys = paramMap.keySet().stream().filter(key -> !key.startsWith("$$") && !"fn".equals(key) && !"distinct".equals(key)).toList();
      if (keys.size() > 1) {
        throw new IllegalArgumentException("聚合函数只支持单个字段。");
      }
      String name = keys.get(0);
      Map<String, Object> aggregateMap = cast(paramMap.get(name));
      List<String> aggregateKeys = aggregateMap.keySet().stream().filter(key -> !key.startsWith("$$") && !"fn".equals(key) && !"distinct".equals(key)).toList();
      if (aggregateKeys.isEmpty()) {
        String fnString = (String) aggregateMap.get("fn");
        AggregateType fn = AggregateType.valueOf(fnString);
        Boolean distinct = (Boolean) aggregateMap.get("distinct");
        return AggregateField.builder().name(name).fn(fn).distinct(distinct).build();
      } else {
        AggregateField child = createAggregateField(aggregateMap);
        return AggregateField.builder().name(name).child(child).build();
      }
    }
    throw new IllegalArgumentException("聚合函数参数错误。");
  }
}
