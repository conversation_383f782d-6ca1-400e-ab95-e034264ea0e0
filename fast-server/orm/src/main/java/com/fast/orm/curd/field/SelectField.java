package com.fast.orm.curd.field;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectField implements Serializable {

  @Serial
  private static final long serialVersionUID = 6676367480150422243L;

  private String name;
  private String alias;
  private List<FieldDirective> directive;
  private List<SelectField> fields;

  public SelectField(String alias, List<FieldDirective> directive) {
    this.alias = alias;
    this.directive = directive;
  }

  public Boolean isRelation() {
    return fields != null && !fields.isEmpty();
  }

  public Boolean isFn() {
    return false;
  }

  public Boolean isGroup() {
    return this.directive.stream().anyMatch(fieldDirective -> "group".equals(fieldDirective.name));
  }


  @Data
  @Builder
  public static class FieldDirective implements Serializable {
    private String name;
    @Singular
    private Map<String, Object> arguments;

    public Object getArgument(String name) {
      return arguments.get(name);
    }
  }
}
