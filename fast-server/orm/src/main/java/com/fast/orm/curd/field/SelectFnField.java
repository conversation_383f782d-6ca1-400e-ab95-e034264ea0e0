package com.fast.orm.curd.field;

import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=true)
public class SelectFnField extends SelectField {

  private AggregateField param;

  @Builder(builderMethodName = "fnBuilder")
  public SelectFnField(String alias,List<FieldDirective> directive, AggregateField param) {
    super(alias, directive);
    this.param = param;
  }


  @Override
  public Boolean isFn() {
    return true;
  }

  public String getName() {
    throw new RuntimeException("聚合字段请查询别名");
  }

}
