package com.fast.orm.curd.having;

import com.fast.orm.curd.condition.FieldCondition;
import com.fast.orm.curd.field.AggregateField;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HavingCondition implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  @Singular("and")
  private List<HavingCondition> and;

  @Singular("or")
  private List<HavingCondition> or;

  private HavingCondition not;

  private AggregateField field;

  private FieldCondition condition;

}
