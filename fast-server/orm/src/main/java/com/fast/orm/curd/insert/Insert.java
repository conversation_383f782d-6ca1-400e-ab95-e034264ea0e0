package com.fast.orm.curd.insert;

import com.fast.core.data.TableMeta;
import com.fast.orm.curd.AbstractCriteria;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: project-management-git
 * @description
 * @author: liunan
 * @create: 2022/3/15 14:51
 **/
public class Insert extends AbstractCriteria<org.jooq.Insert<?>> {

  private final List<Map<String, Object>> params;
  private final Map<Field<?>, Param<?>> columns;

  public Insert(TableMeta tableMeta, Map<Field<?>, Param<?>> columns) {
    this(tableMeta, columns, new ArrayList<>());
  }

  public Insert(TableMeta tableMeta, Map<Field<?>, Param<?>> columns, List<Map<String, Object>> params) {
    super(tableMeta);
    this.columns = columns;
    this.params = params;
    if (this.params != null && !this.params.isEmpty()) {
      this.batchExecute = true;
    }
  }

  public void compile() {
    Table<Record> table = DSL.table(tableMeta.getDbName());
    InsertSetStep<?> insertSetStep = dsl.insertInto(table);
    this.criteria = insertSetStep.set(columns);
    this.compiled = true;
  }

  public List<Map<String, Object>> getBatchParams() {
    return this.params;
  }

  public static Insert create(TableMeta tableMeta, Map<Field<?>, Param<?>> columns) {
    return new Insert(tableMeta, columns);
  }

  public static Insert create(TableMeta tableMeta, Map<Field<?>, Param<?>> columns, List<Map<String, Object>> params) {
    return new Insert(tableMeta, columns, params);
  }


}
