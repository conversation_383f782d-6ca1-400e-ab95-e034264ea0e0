package com.fast.orm.curd.page;


import lombok.Data;

import java.util.List;

@Data
public class GraphQLPage<T> {

  public List<T> content;

  public boolean last;

  public long totalElements;

  public int totalPages;

  public int size;

  public int number;

  public boolean first;

  public int numberOfElements;

  public GraphQLPage(
    List<T> content,
    boolean last,
    long totalElements,
    int totalPages,
    int size,
    int number,
    boolean first,
    int numberOfElements
  ) {
    this.content = content;
    this.last = last;
    this.totalElements = totalElements;
    this.totalPages = totalPages;
    this.size = size;
    this.number = number;
    this.first = first;
    this.numberOfElements = numberOfElements;
  }

  public static <T> GraphQLPage<T> of(List<T> content, long count, Pageable pageable) {
    int pageSize = pageable.getPageSize() == null ? Integer.MAX_VALUE : pageable.getPageSize();
    int pageNumber = pageable.getPageSize() == null ? 0 : pageable.getPageNumber();
    boolean last = count <= (long) pageSize * pageNumber;
    int totalPages = (int) Math.ceil((double) count / pageSize);
    boolean first = pageNumber == 0;
    int numberOfElements = content.size();
    return new GraphQLPage<>(content, last, count, totalPages, pageSize, pageNumber, first, numberOfElements);
  }

}
