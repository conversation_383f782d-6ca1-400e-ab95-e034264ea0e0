package com.fast.orm.curd.page;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Order implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  @Builder.Default
  private Boolean alias = false;
  private String field;
  private Direction direction;

  /**
   * 处理多级
   */
  private Order child;

  public Boolean isAlias() {
    return alias;
  }

  public Boolean isEnd() {
    return child == null;
  }

  public static enum Direction {
    ASC, DESC
  }
}

