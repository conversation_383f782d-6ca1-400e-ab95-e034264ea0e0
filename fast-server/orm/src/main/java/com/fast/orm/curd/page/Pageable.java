package com.fast.orm.curd.page;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Pageable implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  private Integer pageNumber;
  private Integer pageSize;
  @Builder.Default
  private Boolean unPaged = false;

  @Singular("sort")
  private List<Order> sort;


  public static Pageable unPaged() {
    return Pageable.builder().unPaged(true).build();
  }

}
