package com.fast.orm.curd.save;

import com.fast.core.data.ConstraintMeta;
import com.fast.core.data.FieldTypeMeta;
import com.fast.core.data.FieldMeta;
import com.fast.core.data.TableMeta;
import com.fast.core.data.type.BasicDataType;
import com.fast.tools.utils.DateUtils;
import com.fast.orm.curd.Criteria;
import com.fast.orm.curd.insert.Insert;
import com.fast.orm.curd.update.UpdateById;
import io.vertx.core.json.JsonObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fast.core.data.Constants.ID;
import static com.fast.core.data.type.BasicDataType.*;
import static com.fast.tools.utils.FastObjectUtils.cast;


@Slf4j
public class Save {

  private final TableMeta tableMeta;
  private final Map<String, Object> data;

  @Getter
  private final List<Criteria<?>> criteriaList = new ArrayList<>();

  public Save(TableMeta tableMeta, Map<String, Object> data) {
    this.tableMeta = tableMeta;
    this.data = data;
    this.compile();
  }

  public List<Criteria<?>> criteria() {
    return criteriaList;
  }

  private void compile() {
    this.criteriaList.addAll(handleData(tableMeta, data));
  }

  private List<Criteria<?>> handleData(TableMeta tableMeta, Map<String, Object> data) {
    List<Criteria<?>> criteria = new ArrayList<>();
    Table<Record> table = DSL.table(tableMeta.getDbName());
    Map<String, Object> paramMap = new HashMap<>(data);
    if (paramMap.containsKey(ID)) {
      String id = (String) paramMap.get(ID);
      //TODO 默认字段,不应该抽象到这里
      //handleUpdateSystemField(paramMap);
      Pair<List<Criteria<?>>, Map<Field<?>, Param<?>>> updatePair = handleField(table, tableMeta, paramMap);
      criteria.addAll(updatePair.getLeft());
      UpdateById updateById = UpdateById.create(tableMeta, updatePair.getRight(), generateParam(ID, id));
      criteria.add(0, updateById);
    } else {
      // 插入
      String id = UUID.randomUUID().toString().replaceAll("-", "");
      paramMap.put(ID, id);
      //TODO 默认字段,不应该抽象到这里
      // handleInsertSystemField(paramMap);
      Pair<List<Criteria<?>>, Map<Field<?>, Param<?>>> updatePair = handleField(table, tableMeta, paramMap);
      criteria.addAll(updatePair.getLeft());
      Insert insert = Insert.create(tableMeta, updatePair.getRight());
      criteria.add(0, insert);
    }
    return criteria;
  }

  private List<Criteria<?>> handleData(TableMeta tableMeta, List<Map<String, Object>> data) {
    // 根据第一个map，获取到 sql 循环后续的map 转换值获取值。最后组装criteria。
    List<Criteria<?>> criteria = new ArrayList<>();
    Table<Record> table = DSL.table(tableMeta.getDbName());
    Map<String, List<Map<String, Object>>> groupDataMap = data.stream()
      .collect(Collectors.groupingBy(map -> map.containsKey(ID) ? "update" : "insert"));
    List<Map<String, Object>> insertData = groupDataMap.getOrDefault("insert", new ArrayList<>());
    boolean initInsert = true;
    Map<Field<?>, Param<?>> insertColumns = new HashMap<>();
    List<Map<String, Object>> insertParams = new ArrayList<>();
    List<Map<String, Object>> updateData = groupDataMap.getOrDefault("update", new ArrayList<>());
    boolean initUpdate = true;
    Map<Field<?>, Param<?>> updateColumns = new HashMap<>();
    List<Map<String, Object>> updateParams = new ArrayList<>();
    for (Map<String, Object> map : insertData) {
      // 插入时的map，无关联表数据。
      Map<String, Object> paramMap = new HashMap<>(map);
      // 插入
      String id = UUID.randomUUID().toString().replaceAll("-", "");
      paramMap.put(ID, id);
      //TODO 默认字段,不应该抽象到这里
      // handleInsertSystemField(paramMap);
      Triple<List<Criteria<?>>, Map<Field<?>, Param<?>>, Map<String, Object>> insertTriple = handleBatchField(table, tableMeta, paramMap, initInsert);
      criteria.addAll(insertTriple.getLeft());
      if (initInsert) {
        insertColumns = insertTriple.getMiddle();
      }
      insertParams.add(insertTriple.getRight());
      initInsert = false;
    }
    Insert insert = Insert.create(tableMeta, insertColumns, insertParams);
    if (!initInsert) {
      criteria.add(0, insert);
    }
    // 处理更新
    for (Map<String, Object> map : updateData) {
      Map<String, Object> paramMap = new HashMap<>(map);
      String id = (String) paramMap.get(ID);
      //TODO 默认字段,不应该抽象到这里
      // handleUpdateSystemField(paramMap);
      Triple<List<Criteria<?>>, Map<Field<?>, Param<?>>, Map<String, Object>> updateTriple = handleBatchField(table, tableMeta, paramMap, initUpdate);
      criteria.addAll(updateTriple.getLeft());
      if (initUpdate) {
        updateColumns = updateTriple.getMiddle();
      }
      Map<String, Object> param = updateTriple.getRight();
      param.put(ID, id);
      updateParams.add(param);
      initUpdate = false;
    }
    UpdateById updateById = UpdateById.create(tableMeta, updateColumns, generateParam(ID, String.class), updateParams);
    if (!initUpdate) {
      criteria.add(0, updateById);
    }
    return criteria;
  }


//  private void handleInsertSystemField(Map<String, Object> data) {
//    LocalDateTime now = LocalDateTime.now();
//    String createUserId = session.getUserId().orElse("developer");
//    String creator = session.getUsername().orElse("developer");
//    String belongId = session.getBelongId().orElse("0");
//    String modifier = session.getUsername().orElse("developer");
//    String modifyUserId = session.getUserId().orElse("developer");
//    String orgId = session.getOrgId().orElse("0");
//
//    if (StringUtils.isBlank((String) data.get("creator"))) {
//      data.put("creator", creator);
//    }
//    data.putIfAbsent("createDate", now);
//    if (StringUtils.isBlank((String) data.get("createUserId"))) {
//      data.put("createUserId", createUserId);
//    }
//    data.put("modifier", modifier);
//    data.put("modifyUserId", modifyUserId);
//    data.put("modifyDate", now);
//    if (StringUtils.isBlank((String) data.get("belongId"))) {
//      data.put("belongId", belongId);
//    }
//  }


//  private void handleUpdateSystemField(Map<String, Object> data) {
//    LocalDateTime now = LocalDateTime.now();
//    String modifier = session.getUsername().orElse("developer");
//    String modifyUserId = session.getUserId().orElse("developer");
//    data.put("modifier", modifier);
//    data.put("modifyUserId", modifyUserId);
//    data.put("modifyDate", now);
//  }

  private Triple<List<Criteria<?>>, Map<Field<?>, Param<?>>, Map<String, Object>> handleBatchField(Table<Record> table, TableMeta tableMeta, Map<String, Object> paramMap, boolean init) {
    List<Criteria<?>> criteria = new ArrayList<>();
    Map<Field<?>, Param<?>> updateColumns = new HashMap<>();
    Map<String, Object> param = new HashMap<>();
    for (String key : paramMap.keySet()) {
      Object value = paramMap.get(key);
      log.info("key:{},value:{}", key, value);
      FieldMeta fieldMeta = tableMeta.getField(key);
      // 判断是否关联表
      if (FIELD_EMPTY.equals(fieldMeta.getFieldTypeMeta().getType())) {
        if (value instanceof Map mapValue) {
          Map<String, Object> dataMap = new HashMap<>(mapValue);
          TableMeta fTableMeta = tableMeta.getReactionTable(fieldMeta.getName());
          // 处理关联关系
          ConstraintMeta constraintMeta = tableMeta.getConstraint(fieldMeta.getName());
          constraintMeta.getConsColumns().forEach(consColumnsVo -> {
            dataMap.put(consColumnsVo.getFkField(), paramMap.get(consColumnsVo.getField()));
          });
          List<Criteria<?>> subCriteria = handleData(fTableMeta, dataMap);
          criteria.addAll(subCriteria);
        } else if (value instanceof List listValue) {
          TableMeta fTableMeta = tableMeta.getReactionTable(fieldMeta.getName());
          List<Map<String, Object>> dataList = new ArrayList<>();
          for (Object mapValue : listValue) {
            Map<String, Object> dataMap = new HashMap<>(cast(mapValue));
            // 处理关联关系
            ConstraintMeta constraintMeta = tableMeta.getConstraint(fieldMeta.getName());
            constraintMeta.getConsColumns().forEach(consColumnsVo -> {
              dataMap.put(consColumnsVo.getFkField(), paramMap.get(consColumnsVo.getField()));
            });
            dataList.add(dataMap);
          }
          // 一对多适用于批量处理。
          List<Criteria<?>> subCriteria = handleData(fTableMeta, dataList);
          criteria.addAll(subCriteria);
        } else {
          throw new RuntimeException("不支持的数据类型");
        }
      } else {
        Pair<Field<?>, Object> fieldPair = handleField(table, fieldMeta, value);
        if (init) {
          updateColumns.put(fieldPair.getLeft(), generateParam(fieldMeta.getName(), fieldPair.getRight()));
        }
        // 更新map的值
        param.put(key, fieldPair.getRight());
      }
    }
    return Triple.of(criteria, updateColumns, param);
  }

  private Pair<List<Criteria<?>>, Map<Field<?>, Param<?>>> handleField(Table<Record> table, TableMeta tableMeta, Map<String, Object> paramMap) {
    List<Criteria<?>> criteria = new ArrayList<>();
    Map<Field<?>, Param<?>> columns = new HashMap<>();
    for (String key : paramMap.keySet()) {
      Object value = paramMap.get(key);
      FieldMeta fieldMeta = tableMeta.getField(key);
      // 判断是否关联表
      if (FIELD_EMPTY.equals(fieldMeta.getFieldTypeMeta().getType())) {
        if (value instanceof Map mapValue) {
          Map<String, Object> dataMap = new HashMap<>(mapValue);
          TableMeta fTableMeta = tableMeta.getReactionTable(fieldMeta.getName());
          // 处理关联关系
          ConstraintMeta constraintMeta = tableMeta.getConstraint(fieldMeta.getName());
          constraintMeta.getConsColumns().forEach(consColumnsVo -> {
            dataMap.put(consColumnsVo.getFkField(), paramMap.get(consColumnsVo.getField()));
          });
          List<Criteria<?>> subCriteria = handleData(fTableMeta, dataMap);
          criteria.addAll(subCriteria);
        } else if (value instanceof List listValue) {
          TableMeta fTableMeta = tableMeta.getReactionTable(fieldMeta.getName());
          // 处理关联关系
          List<Map<String, Object>> dataList = new ArrayList<>();
          for (Object mapValue : listValue) {
            Map<String, Object> dataMap = new HashMap<>(cast(mapValue));
            // 处理关联关系
            ConstraintMeta constraintMeta = tableMeta.getConstraint(fieldMeta.getName());
            constraintMeta.getConsColumns().forEach(consColumnsVo -> {
              dataMap.put(consColumnsVo.getFkField(), paramMap.get(consColumnsVo.getField()));
            });
            dataList.add(dataMap);
          }
          // 一对多适用于批量处理。
          List<Criteria<?>> subCriteria = handleData(fTableMeta, dataList);
          criteria.addAll(subCriteria);
        } else {
          throw new RuntimeException("不支持的数据类型");
        }
      } else {
        Pair<Field<?>, Object> fieldPair = handleField(table, fieldMeta, value);
        columns.put(fieldPair.getLeft(), generateParam(fieldMeta.getName(), fieldPair.getRight()));
      }
    }
    return Pair.of(criteria, columns);
  }


  private Pair<Field<?>, Object> handleField(Table<Record> currentTable, FieldMeta fieldMeta, Object value) {
    FieldTypeMeta fieldTypeMeta = fieldMeta.getFieldTypeMeta();
    BasicDataType basicDataType = fieldTypeMeta.getType();
    String fieldDbName = fieldMeta.getDbName();
    if (FIELD_JSON.equals(basicDataType)) {
      return Pair.of(DSL.field(fieldDbName, JSONB.class), JSONB.valueOf(JsonObject.mapFrom(value).toString()));
    } else if ("FILES".equals(fieldTypeMeta.getCode())) {
      // todo 附件后续处理
      return Pair.of(null, null);
    } else if (FIELD_BOOLEAN.equals(basicDataType)) {
      if (value instanceof String) {
        value = Boolean.parseBoolean((String) value);
      }
      return Pair.of(generateField(fieldDbName, SQLDataType.BOOLEAN), (Boolean) value);
    } else if (FIELD_INTEGER.equals(basicDataType)) {
      if (value instanceof String) {
        value = Integer.parseInt((String) value);
      } else if (value instanceof Number) {
        value = ((Number) value).intValue();
      }
      assert value instanceof Integer : "应该为Integer类型值！";
      return Pair.of(generateField(fieldDbName, SQLDataType.INTEGER), (Integer) value);
    } else if (FIELD_BIG_DECIMAL.equals(basicDataType)) {
      if (value instanceof String) {
        value = new BigDecimal((String) value);
      } else if (value instanceof Number) {
        value = new BigDecimal(value.toString());
      }
      assert value instanceof BigDecimal : "应该为BigDecimal类型值！";
      return Pair.of(generateField(fieldDbName, SQLDataType.NUMERIC), (BigDecimal) value);
    } else if (FIELD_LOB.equals(basicDataType)) {
      return Pair.of(generateField(fieldDbName, SQLDataType.CLOB), (String) value);
    } else if (FIELD_DATE.equals(basicDataType)) {
      if (value instanceof String) {
        value = DateUtils.parseDate((String) value);
      }
      return Pair.of(generateField(fieldDbName, SQLDataType.LOCALDATE), (LocalDate) value);
    } else if (FIELD_DATE_TIME.equals(basicDataType)) {
      if (value instanceof String) {
        value = DateUtils.parseDateTime((String) value);
      }
      return Pair.of(generateField(fieldDbName, SQLDataType.LOCALDATETIME), (LocalDateTime) value);
    } else {
      return Pair.of(generateField(fieldDbName, SQLDataType.VARCHAR), (String) value);
    }
  }

  private <T> Param<T> generateParam(String name, Class<T> clazz) {
    return DSL.param("{" + name + "}", clazz);
  }

  private <T> Param<T> generateParam(String name, T value) {
    return DSL.param("{" + name + "}", value);
  }

  private Field<?> generateField(String name) {
    return DSL.field(name);
  }

  private Field<?> generateField(String name, DataType<?> type) {
    return DSL.field(name, type);
  }


  public static Save create(TableMeta tableMeta, Map<String, Object> data) {
    return new Save(tableMeta, data);
  }

}
