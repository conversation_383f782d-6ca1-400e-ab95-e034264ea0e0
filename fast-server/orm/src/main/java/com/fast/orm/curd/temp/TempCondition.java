package com.fast.orm.curd.temp;

import com.fast.orm.curd.condition.FieldCondition;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TempCondition implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  @Singular("and")
  private List<TempCondition> and;

  @Singular("or")
  private List<TempCondition> or;

  private TempCondition not;

  private String alias;

  private FieldCondition condition;

  public Map<String, Object> toMap() {
    Map<String, Object> resultMap = new HashMap<>();
    if (ObjectUtils.isNotEmpty(this.getAnd())) {
      List<Map<String, Object>> andMaps = this.getAnd().stream().map(TempCondition::toMap).collect(Collectors.toList());
      resultMap.put("_and", andMaps);
    }
    if (ObjectUtils.isNotEmpty(this.getOr())) {
      List<Map<String, Object>> orMaps = this.getOr().stream().map(TempCondition::toMap).collect(Collectors.toList());
      resultMap.put("_or", orMaps);
    }
    if (ObjectUtils.isNotEmpty(this.getNot())) {
      resultMap.put("_not", this.getNot().toMap());
    }
    if (ObjectUtils.isNotEmpty(this.getAlias())) {
      resultMap.put("_alias", this.getAlias());
    }
    if (ObjectUtils.isNotEmpty(this.getCondition())) {
      resultMap.put("_condition", this.getCondition().toMap());
    }

    return resultMap;
  }

  public TempCondition and(TempCondition... and) {
    if (this.and == null) {
      this.and = new ArrayList<>();
    }
    this.and.addAll(List.of(and));
    return this;
  }

  public TempCondition or(TempCondition... or) {
    if (this.or == null) {
      this.or = new ArrayList<>();
    }
    this.or.addAll(List.of(or));
    return this;
  }

  public TempCondition not(TempCondition not) {
    this.not = not;
    return this;
  }


}
