package com.fast.orm.curd.update;

import com.fast.core.data.ConstraintMeta;
import com.fast.core.data.FieldMeta;
import com.fast.core.data.TableMeta;
import com.fast.orm.curd.AbstractCriteria;
import com.fast.orm.curd.FieldUtils;
import com.fast.orm.curd.JoinType;
import com.fast.orm.curd.TableFactory;
import com.fast.orm.curd.condition.*;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fast.orm.curd.CriteriaUtils.*;

public class Update extends AbstractCriteria<org.jooq.Update<?>> {

  // 更新数据
  private Map<String, Object> data;
  private BaseTypeCondition whereCondition;

  public Update(TableMeta tableMeta, BaseTypeCondition whereCondition, Map<String, Object> data) {
    super(tableMeta);
    this.data = data;
    this.whereCondition = whereCondition;
  }

  public void compile() {
    Table<Record> table = tableFactory.getMainTable();
    if (whereCondition == null) {
      throw new RuntimeException("更新时where条件不能为空");
    }
    this.criteria = dsl.update(table)
      .set(data)
      .where(convertCondition(table, tableMeta, whereCondition, tableFactory));
    this.compiled = true;
  }

  private Condition convertCondition(Table<Record> currentTable, TableMeta currentTableMeta, BaseTypeCondition whereCondition, TableFactory tableFactory) {
    Condition condition = DSL.noCondition();
    if (whereCondition.getFieldConditions() != null) {
      List<Condition> andConditions = whereCondition.getFieldConditions().entrySet().stream().map((entry) -> {
        if (entry.getValue() instanceof BaseTypeCondition) {
          // 关联表处理。
          return convertBastTypeCondition(entry.getKey(), currentTable, currentTableMeta, (BaseTypeCondition) entry.getValue(), tableFactory);
        } else {
          return convertBasicCondition(entry.getKey(), currentTable, currentTableMeta, entry.getValue(), tableFactory, this::conditionField);
        }
      }).flatMap(java.util.Collection::stream).toList();
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (whereCondition.getAnd() != null) {
      List<Condition> andConditions = whereCondition.getAnd()
        .stream()
        .map(baseType -> convertCondition(currentTable, currentTableMeta, baseType, tableFactory))
        .collect(Collectors.toList());
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (whereCondition.getOr() != null) {
      List<Condition> orConditions = whereCondition.getOr()
        .stream()
        .map(baseType -> convertCondition(currentTable, currentTableMeta, baseType, tableFactory))
        .collect(Collectors.toList());
      if (!orConditions.isEmpty()) {
        condition = condition.and(DSL.or(orConditions));
      }
    }
    if (whereCondition.getNot() != null) {
      condition = condition.andNot(
        convertCondition(currentTable, currentTableMeta, whereCondition.getNot(), tableFactory));
    }
    if (whereCondition.getExist() != null) {
      List<Select<?>> selects = convertExistSelect(currentTable, currentTableMeta, whereCondition.getExist(), tableFactory);
      for (Select<?> select : selects) {
        condition = condition.andExists(select);
      }
    }
    return condition;
  }

  public List<Condition> convertBastTypeCondition(String key, Table<Record> currentTable, TableMeta currentTableMeta, BaseTypeCondition value, TableFactory tableFactory) {
    List<Condition> resultCondition = new ArrayList<>();
    FieldMeta fieldMeta = currentTableMeta.getField(key);
    ConstraintMeta constraintMeta = currentTableMeta.getConstraint(fieldMeta.getName());
    if (constraintMeta == null) {
      throw new RuntimeException("没有找到关联字段：" + fieldMeta.getName());
    }
    if (!JoinType.NONE.equals(constraintMeta.getJoinType())) { // join处理
      // 暂时不支持join
    } else { // 子查询处理
      resultCondition.add(relationSubQuery(constraintMeta, currentTable, currentTableMeta, value, tableFactory));
    }
    return resultCondition;
  }

  public <T> Field<T> conditionField(String fieldPath, Class<T> clazz, Table<Record> currentTable, TableMeta currentTableMeta, TableFactory tableFactory) {
    if (!fieldPath.contains(".")) { // 不带点，单表处理
      FieldMeta currentFieldMeta = currentTableMeta.getField(fieldPath);
      return FieldUtils.generateField(currentTable, currentFieldMeta.getDbName(), clazz);
    } else { // 带点则先关联再处理
      throw new RuntimeException("删除语句，暂不支持关联！");
    }
  }

  public static Update create(TableMeta tableMeta, BaseTypeCondition whereCondition, Map<String, Object> data) {
    return new Update(tableMeta, whereCondition, data);
  }

}
