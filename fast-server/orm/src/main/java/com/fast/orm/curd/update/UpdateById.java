package com.fast.orm.curd.update;

import com.fast.core.data.TableMeta;
import com.fast.orm.curd.AbstractCriteria;
import com.fast.orm.curd.FieldUtils;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fast.core.data.Constants.ID;

public class UpdateById extends AbstractCriteria<org.jooq.Update<?>> {

  private final Map<Field<?>, Param<?>> columns;
  private final List<Map<String, Object>> params;
  private final Param<String> idParam;

  public UpdateById(TableMeta tableMeta, Map<Field<?>, Param<?>> columns, Param<String> idParam) {
    this(tableMeta, columns, idParam, new ArrayList<>());
  }

  public UpdateById(TableMeta tableMeta, Map<Field<?>, Param<?>> columns, Param<String> idParam, List<Map<String, Object>> params) {
    super(tableMeta);
    this.columns = columns;
    this.idParam = idParam;
    this.params = params;
    if (this.params != null && !this.params.isEmpty()) {
      this.batchExecute = true;
    }
  }

  public void compile() {
    Table<Record> table = DSL.table(tableMeta.getDbName());
    UpdateSetStep<?> updateSetStep = dsl.update(table);
    this.criteria = updateSetStep
      .set(columns)
      .where(FieldUtils.generateField(table, ID, String.class).eq(idParam));
    this.compiled = true;
  }

  @Override
  public List<Map<String, Object>> getBatchParams() {
    return this.params;
  }

  public static UpdateById create(TableMeta tableMeta, Map<Field<?>, Param<?>> columns, Param<String> idParam) {
    return new UpdateById(tableMeta, columns, idParam);
  }

  public static UpdateById create(TableMeta tableMeta, Map<Field<?>, Param<?>> columns, Param<String> idParam, List<Map<String, Object>> params) {
    return new UpdateById(tableMeta, columns, idParam, params);
  }

}
