package com.fast.orm.type;

import com.fast.orm.curd.condition.BaseTypeCondition;
import com.fast.orm.curd.page.Order;

import java.util.List;

/**
 * 字段操作接口定义
 * 通过接口分离不同类型字段支持的操作
 */
public interface FieldOperations {

    /**
     * 基础操作接口 - 所有字段类型都支持
     */
    interface BaseOperations<T> {
        BaseTypeCondition eq(T value);
        BaseTypeCondition neq(T value);
        BaseTypeCondition isNull();
        BaseTypeCondition isNotNull();
        BaseTypeCondition in(List<T> values);
        BaseTypeCondition notIn(List<T> values);
        Order asc();
        Order desc();
    }

    /**
     * 字符串操作接口 - 只有字符串字段支持
     */
    interface StringOperations {
        BaseTypeCondition like(String value);
        BaseTypeCondition nlike(String value);
    }

    /**
     * 比较操作接口 - 数字和日期字段支持
     */
    interface ComparableOperations<T> {
        BaseTypeCondition gt(T value);
        BaseTypeCondition gte(T value);
        BaseTypeCondition lt(T value);
        BaseTypeCondition lte(T value);
        BaseTypeCondition between(T start, T end);
    }

    /**
     * 字符串字段接口 - 组合基础操作和字符串操作
     */
    interface StringField<T> extends BaseOperations<T>, StringOperations {
    }

    /**
     * 数字字段接口 - 组合基础操作和比较操作
     */
    interface NumberField<T> extends BaseOperations<T>, ComparableOperations<T> {
    }

    /**
     * 日期字段接口 - 组合基础操作和比较操作
     */
    interface DateField<T> extends BaseOperations<T>, ComparableOperations<T> {
    }

    /**
     * 布尔字段接口 - 只有基础操作
     */
    interface BooleanField<T> extends BaseOperations<T> {
    }
}
