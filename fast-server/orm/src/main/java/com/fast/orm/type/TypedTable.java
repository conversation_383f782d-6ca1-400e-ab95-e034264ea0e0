package com.fast.orm.type;

import com.fast.core.data.FieldMeta;
import com.fast.core.data.TableMeta;
import com.fast.core.data.type.BasicDataType;
import com.fast.core.data.utils.EntityMetadataParser;
import com.fast.orm.curd.field.SelectField;

import java.util.List;

public abstract class TypedTable<T> {

  public abstract Class<T> getEntityClass();

  public TableMeta getTableMeta() {
    return EntityMetadataParser.parseEntity(getEntityClass());
  }

  public List<? extends TypedField<?>> allFields() {
    return getTableMeta().getFieldMap().values().stream().map(this::createTypedField).toList();
  }

  /**
   * 根据字段元数据创建相应类型的TypedField
   */
  private TypedField<?> createTypedField(FieldMeta fieldMeta) {
    SelectField selectField = SelectField.builder().name(fieldMeta.getName()).build();
    BasicDataType dataType = fieldMeta.getFieldTypeMeta().getType();

    return switch (dataType) {
      case FIELD_STRING, FIELD_LOB -> new TypedField.StringField(selectField);
      case FIELD_INTEGER -> new TypedField.IntegerField(selectField);
      case FIELD_BIG_DECIMAL -> new TypedField.BigDecimalField(selectField);
      case FIELD_DATE -> new TypedField.DateField(selectField);
      case FIELD_DATE_TIME -> new TypedField.DateTimeField(selectField);
      case FIELD_BOOLEAN -> new TypedField.BooleanField(selectField);
      case FIELD_JSON, FIELD_EMPTY -> new TypedField.StringField(selectField); // 默认为字符串类型
      default -> new TypedField.StringField(selectField); // 兜底方案
    };
  }
}
