package com.fast.orm.query;

import com.fast.orm.curd.AggregateType;
import com.fast.orm.curd.condition.BaseTypeCondition;
import com.fast.orm.curd.condition.FieldCondition;
import com.fast.orm.curd.condition.StringCondition;
import com.fast.orm.curd.field.AggregateField;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.curd.field.SelectFnField;
import com.fast.orm.curd.having.HavingCondition;
import com.fast.orm.curd.page.Order;
import com.fast.orm.curd.temp.TempCondition;

import java.util.Collections;

public class Field {

  private SelectField selectField;

  public Field(SelectField selectField) {
    this.selectField = selectField;
  }

  // 聚合函数
  public SelectFnField count() {
    AggregateField aggregateField = AggregateField.builder().name(this.selectField.getName()).fn(AggregateType.COUNT).build();
    return SelectFnField.fnBuilder()
      .param(aggregateField).build();
  }

  public Field group() {
    SelectField.FieldDirective fieldDirective = SelectField.FieldDirective.builder().name("group").build();
    this.selectField.setDirective(Collections.singletonList(fieldDirective));
    return this;
  }

  public Field as(String alias) {
    this.selectField.setAlias(alias);
    return this;
  }

  // 条件
  public BaseTypeCondition eq(String value) {
    StringCondition stringCondition = StringCondition.builder()
      .eq(value).build();
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), stringCondition).build();
  }

  public BaseTypeCondition neq(String value) {
    StringCondition stringCondition = StringCondition.builder()
      .neq(value).build();
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), stringCondition).build();
  }

  public BaseTypeCondition like(String value) {
    StringCondition stringCondition = StringCondition.builder()
      .like(value).build();
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), stringCondition).build();
  }

  public BaseTypeCondition nlike(String value) {
    StringCondition stringCondition = StringCondition.builder()
      .nlike(value).build();
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), stringCondition).build();
  }

  // 排序
  public Order asc() {
    return Order.builder().field(this.selectField.getName()).direction(Order.Direction.ASC).build();
  }

  public Order desc() {
    return Order.builder().field(this.selectField.getName()).direction(Order.Direction.ASC).build();
  }

  public SelectField getField() {
    return this.selectField;
  }


  public static Having having(SelectFnField field) {
    return new Having(field.getParam());
  }

  public static Temp temp(String field) {
    return new Temp(field);
  }


}
