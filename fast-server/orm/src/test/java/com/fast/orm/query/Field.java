package com.fast.orm.query;

import com.fast.core.data.type.BasicDataType;
import com.fast.orm.curd.AggregateType;
import com.fast.orm.curd.condition.*;
import com.fast.orm.curd.field.AggregateField;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.curd.field.SelectFnField;
import com.fast.orm.curd.having.HavingCondition;
import com.fast.orm.curd.page.Order;
import com.fast.orm.curd.temp.TempCondition;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

public class Field<T> {

  private SelectField selectField;
  private BasicDataType dataType;

  public Field(SelectField selectField) {
    this.selectField = selectField;
    this.dataType = BasicDataType.FIELD_STRING; // 默认为字符串类型
  }

  public Field(SelectField selectField, BasicDataType dataType) {
    this.selectField = selectField;
    this.dataType = dataType;
  }

  // 聚合函数
  public SelectFnField count() {
    AggregateField aggregateField = AggregateField.builder().name(this.selectField.getName()).fn(AggregateType.COUNT).build();
    return SelectFnField.fnBuilder()
      .param(aggregateField).build();
  }

  public Field<T> group() {
    SelectField.FieldDirective fieldDirective = SelectField.FieldDirective.builder().name("group").build();
    this.selectField.setDirective(Collections.singletonList(fieldDirective));
    return this;
  }

  public Field<T> as(String alias) {
    this.selectField.setAlias(alias);
    return this;
  }

  // 通用条件方法 - 所有类型都支持
  public BaseTypeCondition eq(T value) {
    BaseCondition<?> condition = createConditionWithValue("eq", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition neq(T value) {
    BaseCondition<?> condition = createConditionWithValue("neq", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition isNull() {
    BaseCondition<?> condition = createConditionWithValue("isNull", true);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition isNotNull() {
    BaseCondition<?> condition = createConditionWithValue("isNull", false);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition in(List<T> values) {
    BaseCondition<?> condition = createConditionWithValue("in", values);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition notIn(List<T> values) {
    BaseCondition<?> condition = createConditionWithValue("nin", values);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  // 字符串特有的条件方法
  public BaseTypeCondition like(String value) {
    if (!isStringType()) {
      throw new UnsupportedOperationException("like operation is only supported for string fields");
    }
    StringCondition stringCondition = StringCondition.builder().like(value).build();
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), stringCondition).build();
  }

  public BaseTypeCondition nlike(String value) {
    if (!isStringType()) {
      throw new UnsupportedOperationException("nlike operation is only supported for string fields");
    }
    StringCondition stringCondition = StringCondition.builder().nlike(value).build();
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), stringCondition).build();
  }

  // 数字类型的比较方法
  public BaseTypeCondition gt(T value) {
    if (!isComparableType()) {
      throw new UnsupportedOperationException("gt operation is only supported for comparable fields (numbers, dates)");
    }
    BaseCondition<?> condition = createConditionWithValue("gt", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition gte(T value) {
    if (!isComparableType()) {
      throw new UnsupportedOperationException("gte operation is only supported for comparable fields (numbers, dates)");
    }
    BaseCondition<?> condition = createConditionWithValue("gte", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition lt(T value) {
    if (!isComparableType()) {
      throw new UnsupportedOperationException("lt operation is only supported for comparable fields (numbers, dates)");
    }
    BaseCondition<?> condition = createConditionWithValue("lt", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition lte(T value) {
    if (!isComparableType()) {
      throw new UnsupportedOperationException("lte operation is only supported for comparable fields (numbers, dates)");
    }
    BaseCondition<?> condition = createConditionWithValue("lte", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  public BaseTypeCondition between(T start, T end) {
    if (!isComparableType()) {
      throw new UnsupportedOperationException("between operation is only supported for comparable fields (numbers, dates)");
    }
    BaseCondition<?> condition = createConditionWithValue("between", List.of(start, end));
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  // 排序
  public Order asc() {
    return Order.builder().field(this.selectField.getName()).direction(Order.Direction.ASC).build();
  }

  public Order desc() {
    return Order.builder().field(this.selectField.getName()).direction(Order.Direction.DESC).build();
  }

  public SelectField getField() {
    return this.selectField;
  }

  public BasicDataType getDataType() {
    return this.dataType;
  }

  // 辅助方法
  private boolean isStringType() {
    return dataType == BasicDataType.FIELD_STRING;
  }

  private boolean isComparableType() {
    return dataType == BasicDataType.FIELD_INTEGER ||
           dataType == BasicDataType.FIELD_BIG_DECIMAL ||
           dataType == BasicDataType.FIELD_DATE ||
           dataType == BasicDataType.FIELD_DATE_TIME;
  }

  @SuppressWarnings("unchecked")
  private BaseCondition<?> createConditionWithValue(String operation, Object value) {
    return switch (dataType) {
      case FIELD_STRING -> createStringCondition(operation, value);
      case FIELD_INTEGER -> createIntCondition(operation, value);
      case FIELD_BIG_DECIMAL -> createBigDecimalCondition(operation, value);
      case FIELD_DATE -> createDateCondition(operation, value);
      case FIELD_DATE_TIME -> createDateTimeCondition(operation, value);
      case FIELD_BOOLEAN -> createBooleanCondition(operation, value);
      default -> throw new UnsupportedOperationException("Unsupported data type: " + dataType);
    };
  }

  private StringCondition createStringCondition(String operation, Object value) {
    StringCondition.StringConditionBuilder builder = StringCondition.builder();
    return switch (operation) {
      case "eq" -> builder.eq((String) value).build();
      case "neq" -> builder.neq((String) value).build();
      case "like" -> builder.like((String) value).build();
      case "nlike" -> builder.nlike((String) value).build();
      case "isNull" -> builder.isNull((Boolean) value).build();
      case "in" -> builder.in((List<String>) value).build();
      case "nin" -> builder.nin((List<String>) value).build();
      default -> throw new UnsupportedOperationException("Unsupported operation for string: " + operation);
    };
  }

  private IntCondition createIntCondition(String operation, Object value) {
    IntCondition.IntConditionBuilder builder = IntCondition.builder();
    return switch (operation) {
      case "eq" -> builder.eq((Integer) value).build();
      case "neq" -> builder.neq((Integer) value).build();
      case "gt" -> builder.gt((Integer) value).build();
      case "gte" -> builder.gte((Integer) value).build();
      case "lt" -> builder.lt((Integer) value).build();
      case "lte" -> builder.lte((Integer) value).build();
      case "between" -> builder.between((List<Integer>) value).build();
      case "isNull" -> builder.isNull((Boolean) value).build();
      case "in" -> builder.in((List<Integer>) value).build();
      case "nin" -> builder.nin((List<Integer>) value).build();
      default -> throw new UnsupportedOperationException("Unsupported operation for integer: " + operation);
    };
  }

  private BigDecimalCondition createBigDecimalCondition(String operation, Object value) {
    BigDecimalCondition.BigDecimalConditionBuilder builder = BigDecimalCondition.builder();
    return switch (operation) {
      case "eq" -> builder.eq((BigDecimal) value).build();
      case "neq" -> builder.neq((BigDecimal) value).build();
      case "gt" -> builder.gt((BigDecimal) value).build();
      case "gte" -> builder.gte((BigDecimal) value).build();
      case "lt" -> builder.lt((BigDecimal) value).build();
      case "lte" -> builder.lte((BigDecimal) value).build();
      case "between" -> builder.between((List<BigDecimal>) value).build();
      case "isNull" -> builder.isNull((Boolean) value).build();
      case "in" -> builder.in((List<BigDecimal>) value).build();
      case "nin" -> builder.nin((List<BigDecimal>) value).build();
      default -> throw new UnsupportedOperationException("Unsupported operation for BigDecimal: " + operation);
    };
  }

  private DateCondition createDateCondition(String operation, Object value) {
    DateCondition.DateConditionBuilder builder = DateCondition.builder();
    return switch (operation) {
      case "eq" -> builder.eq((LocalDate) value).build();
      case "neq" -> builder.neq((LocalDate) value).build();
      case "gt" -> builder.gt((LocalDate) value).build();
      case "gte" -> builder.gte((LocalDate) value).build();
      case "lt" -> builder.lt((LocalDate) value).build();
      case "lte" -> builder.lte((LocalDate) value).build();
      case "between" -> builder.between((List<LocalDate>) value).build();
      case "isNull" -> builder.isNull((Boolean) value).build();
      case "in" -> builder.in((List<LocalDate>) value).build();
      case "nin" -> builder.nin((List<LocalDate>) value).build();
      default -> throw new UnsupportedOperationException("Unsupported operation for LocalDate: " + operation);
    };
  }

  private DateTimeCondition createDateTimeCondition(String operation, Object value) {
    DateTimeCondition.DateTimeConditionBuilder builder = DateTimeCondition.builder();
    return switch (operation) {
      case "eq" -> builder.eq((LocalDateTime) value).build();
      case "neq" -> builder.neq((LocalDateTime) value).build();
      case "gt" -> builder.gt((LocalDateTime) value).build();
      case "gte" -> builder.gte((LocalDateTime) value).build();
      case "lt" -> builder.lt((LocalDateTime) value).build();
      case "lte" -> builder.lte((LocalDateTime) value).build();
      case "between" -> builder.between((List<LocalDateTime>) value).build();
      case "isNull" -> builder.isNull((Boolean) value).build();
      case "in" -> builder.in((List<LocalDateTime>) value).build();
      case "nin" -> builder.nin((List<LocalDateTime>) value).build();
      default -> throw new UnsupportedOperationException("Unsupported operation for LocalDateTime: " + operation);
    };
  }

  private BooleanCondition createBooleanCondition(String operation, Object value) {
    BooleanCondition.BooleanConditionBuilder builder = BooleanCondition.builder();
    return switch (operation) {
      case "eq" -> builder.eq((Boolean) value).build();
      case "neq" -> builder.neq((Boolean) value).build();
      case "isNull" -> builder.isNull((Boolean) value).build();
      default -> throw new UnsupportedOperationException("Unsupported operation for Boolean: " + operation);
    };
  }

  // 静态工厂方法
  public static Field<String> stringField(String name) {
    SelectField selectField = SelectField.builder().name(name).build();
    return new Field<>(selectField, BasicDataType.FIELD_STRING);
  }

  public static Field<Integer> intField(String name) {
    SelectField selectField = SelectField.builder().name(name).build();
    return new Field<>(selectField, BasicDataType.FIELD_INTEGER);
  }

  public static Field<BigDecimal> bigDecimalField(String name) {
    SelectField selectField = SelectField.builder().name(name).build();
    return new Field<>(selectField, BasicDataType.FIELD_BIG_DECIMAL);
  }

  public static Field<LocalDate> dateField(String name) {
    SelectField selectField = SelectField.builder().name(name).build();
    return new Field<>(selectField, BasicDataType.FIELD_DATE);
  }

  public static Field<LocalDateTime> dateTimeField(String name) {
    SelectField selectField = SelectField.builder().name(name).build();
    return new Field<>(selectField, BasicDataType.FIELD_DATE_TIME);
  }

  public static Field<Boolean> booleanField(String name) {
    SelectField selectField = SelectField.builder().name(name).build();
    return new Field<>(selectField, BasicDataType.FIELD_BOOLEAN);
  }

  public static Having having(SelectFnField field) {
    return new Having(field.getParam());
  }

  public static Temp temp(String field) {
    return new Temp(field);
  }

}
