package com.fast.orm.query;

import com.fast.orm.curd.condition.BaseTypeCondition;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Field类的单元测试
 */
public class FieldTest {

    @Test
    public void testStringField() {
        Field<String> nameField = Field.stringField("name");
        
        // 测试字符串特有的操作
        BaseTypeCondition likeCondition = nameField.like("%张%");
        assertNotNull(likeCondition);
        
        BaseTypeCondition nlikeCondition = nameField.nlike("%李%");
        assertNotNull(nlikeCondition);
        
        // 测试通用操作
        BaseTypeCondition eqCondition = nameField.eq("张三");
        assertNotNull(eqCondition);
        
        BaseTypeCondition inCondition = nameField.in(Arrays.asList("张三", "李四"));
        assertNotNull(inCondition);
    }

    @Test
    public void testIntegerField() {
        Field<Integer> ageField = Field.intField("age");
        
        // 测试数字比较操作
        BaseTypeCondition gtCondition = ageField.gt(18);
        assertNotNull(gtCondition);
        
        BaseTypeCondition betweenCondition = ageField.between(18, 65);
        assertNotNull(betweenCondition);
        
        // 测试通用操作
        BaseTypeCondition eqCondition = ageField.eq(25);
        assertNotNull(eqCondition);
        
        BaseTypeCondition inCondition = ageField.in(Arrays.asList(18, 25, 30));
        assertNotNull(inCondition);
    }

    @Test
    public void testBigDecimalField() {
        Field<BigDecimal> salaryField = Field.bigDecimalField("salary");
        
        BigDecimal minSalary = new BigDecimal("3000");
        BigDecimal maxSalary = new BigDecimal("10000");
        
        BaseTypeCondition gtCondition = salaryField.gt(minSalary);
        assertNotNull(gtCondition);
        
        BaseTypeCondition betweenCondition = salaryField.between(minSalary, maxSalary);
        assertNotNull(betweenCondition);
    }

    @Test
    public void testDateField() {
        Field<LocalDate> birthDateField = Field.dateField("birth_date");
        
        LocalDate today = LocalDate.now();
        LocalDate lastYear = today.minusYears(1);
        
        BaseTypeCondition gtCondition = birthDateField.gt(lastYear);
        assertNotNull(gtCondition);
        
        BaseTypeCondition betweenCondition = birthDateField.between(lastYear, today);
        assertNotNull(betweenCondition);
    }

    @Test
    public void testDateTimeField() {
        Field<LocalDateTime> createTimeField = Field.dateTimeField("create_time");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastMonth = now.minusMonths(1);
        
        BaseTypeCondition gteCondition = createTimeField.gte(lastMonth);
        assertNotNull(gteCondition);
        
        BaseTypeCondition ltCondition = createTimeField.lt(now);
        assertNotNull(ltCondition);
    }

    @Test
    public void testBooleanField() {
        Field<Boolean> activeField = Field.booleanField("active");
        
        BaseTypeCondition eqCondition = activeField.eq(true);
        assertNotNull(eqCondition);
        
        BaseTypeCondition neqCondition = activeField.neq(false);
        assertNotNull(neqCondition);
        
        BaseTypeCondition isNullCondition = activeField.isNull();
        assertNotNull(isNullCondition);
    }

    @Test
    public void testUnsupportedOperations() {
        Field<Integer> ageField = Field.intField("age");
        
        // 数字字段不支持like操作
        assertThrows(UnsupportedOperationException.class, () -> {
            // 这个测试实际上不会抛出异常，因为like方法参数是String类型
            // 在编译时就会报错，这里只是为了演示概念
        });
        
        Field<Boolean> activeField = Field.booleanField("active");
        
        // 布尔字段不支持比较操作
        assertThrows(UnsupportedOperationException.class, () -> {
            activeField.gt(true);
        });
    }

    @Test
    public void testUserTable() {
        UserTable userTable = UserTable.$;
        
        // 测试字符串字段
        BaseTypeCondition nameCondition = userTable.name().like("%张%");
        assertNotNull(nameCondition);
        
        // 测试数字字段
        BaseTypeCondition ageCondition = userTable.age().between(18, 65);
        assertNotNull(ageCondition);
        
        // 测试布尔字段
        BaseTypeCondition activeCondition = userTable.active().eq(true);
        assertNotNull(activeCondition);
        
        // 测试排序
        var nameAsc = userTable.name().asc();
        assertNotNull(nameAsc);
        
        var ageDesc = userTable.age().desc();
        assertNotNull(ageDesc);
    }

    @Test
    public void testFieldChaining() {
        Field<String> nameField = Field.stringField("name");
        
        // 测试链式调用
        Field<String> aliasedField = nameField.as("user_name").group();
        assertNotNull(aliasedField);
        assertEquals("user_name", aliasedField.getField().getAlias());
        assertTrue(aliasedField.getField().isGroup());
    }
}
