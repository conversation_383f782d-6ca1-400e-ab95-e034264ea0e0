package com.fast.orm.query;

import com.fast.orm.curd.condition.BaseTypeCondition;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * Field类使用示例
 * 演示不同数据类型字段的条件操作
 */
public class FieldUsageExample {

    public static void main(String[] args) {
        // 字符串字段示例
        stringFieldExample();
        
        // 数字字段示例
        numberFieldExample();
        
        // 日期字段示例
        dateFieldExample();
        
        // 布尔字段示例
        booleanFieldExample();
        
        // 使用UserTable示例
        userTableExample();
    }

    /**
     * 字符串字段示例
     */
    private static void stringFieldExample() {
        System.out.println("=== 字符串字段示例 ===");
        
        Field<String> nameField = Field.stringField("name");
        
        // 字符串特有的操作
        BaseTypeCondition condition1 = nameField.eq("张三");
        BaseTypeCondition condition2 = nameField.like("%张%");
        BaseTypeCondition condition3 = nameField.nlike("%李%");
        BaseTypeCondition condition4 = nameField.in(Arrays.asList("张三", "李四", "王五"));
        
        System.out.println("字符串字段支持: eq, neq, like, nlike, in, notIn, isNull, isNotNull");
    }

    /**
     * 数字字段示例
     */
    private static void numberFieldExample() {
        System.out.println("=== 数字字段示例 ===");
        
        Field<Integer> ageField = Field.intField("age");
        Field<BigDecimal> salaryField = Field.bigDecimalField("salary");
        
        // 数字比较操作
        BaseTypeCondition condition1 = ageField.gt(18);
        BaseTypeCondition condition2 = ageField.gte(18);
        BaseTypeCondition condition3 = ageField.lt(65);
        BaseTypeCondition condition4 = ageField.lte(65);
        BaseTypeCondition condition5 = ageField.between(18, 65);
        BaseTypeCondition condition6 = ageField.eq(25);
        
        BaseTypeCondition condition7 = salaryField.gt(new BigDecimal("5000"));
        BaseTypeCondition condition8 = salaryField.between(new BigDecimal("3000"), new BigDecimal("10000"));
        
        System.out.println("数字字段支持: eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull");
        
        // 尝试在数字字段上使用like会抛出异常
        try {
            // ageField.like("25"); // 这行会编译错误，因为like方法参数是String
            System.out.println("数字字段不支持like操作");
        } catch (Exception e) {
            System.out.println("错误: " + e.getMessage());
        }
    }

    /**
     * 日期字段示例
     */
    private static void dateFieldExample() {
        System.out.println("=== 日期字段示例 ===");
        
        Field<LocalDate> birthDateField = Field.dateField("birth_date");
        Field<LocalDateTime> createTimeField = Field.dateTimeField("create_time");
        
        LocalDate today = LocalDate.now();
        LocalDate lastYear = today.minusYears(1);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastMonth = now.minusMonths(1);
        
        // 日期比较操作
        BaseTypeCondition condition1 = birthDateField.gt(lastYear);
        BaseTypeCondition condition2 = birthDateField.between(lastYear, today);
        BaseTypeCondition condition3 = createTimeField.gte(lastMonth);
        BaseTypeCondition condition4 = createTimeField.lt(now);
        
        System.out.println("日期字段支持: eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull");
    }

    /**
     * 布尔字段示例
     */
    private static void booleanFieldExample() {
        System.out.println("=== 布尔字段示例 ===");
        
        Field<Boolean> activeField = Field.booleanField("active");
        
        // 布尔值操作
        BaseTypeCondition condition1 = activeField.eq(true);
        BaseTypeCondition condition2 = activeField.neq(false);
        BaseTypeCondition condition3 = activeField.isNull();
        
        System.out.println("布尔字段支持: eq, neq, isNull, isNotNull");
    }

    /**
     * 使用UserTable示例
     */
    private static void userTableExample() {
        System.out.println("=== UserTable使用示例 ===");
        
        UserTable userTable = UserTable.$;
        
        // 字符串字段操作
        BaseTypeCondition nameCondition = userTable.name().like("%张%");
        BaseTypeCondition usernameCondition = userTable.username().eq("zhangsan");
        
        // 数字字段操作
        BaseTypeCondition ageCondition = userTable.age().between(18, 65);
        BaseTypeCondition ageGtCondition = userTable.age().gt(25);
        
        // 布尔字段操作
        BaseTypeCondition activeCondition = userTable.active().eq(true);
        
        // 排序
        var nameAsc = userTable.name().asc();
        var ageDesc = userTable.age().desc();
        
        System.out.println("UserTable字段根据类型提供相应的操作方法");
    }
}
