package com.fast.orm.query;

import com.fast.orm.curd.condition.FieldCondition;
import com.fast.orm.curd.field.AggregateField;
import com.fast.orm.curd.field.SelectFnField;
import com.fast.orm.curd.having.HavingCondition;

public class Having {

  private final AggregateField field;

  public Having(AggregateField field) {
    this.field = field;
  }

  public HavingCondition gt(Object value) {
    FieldCondition fieldCondition = FieldCondition.builder()
      .gt(value)
      .build();
    return HavingCondition.builder()
      .field(field)
      .condition(fieldCondition)
      .build();
  }

  public static Having having(SelectFnField field) {
    return new Having(field.getParam());
  }

}
