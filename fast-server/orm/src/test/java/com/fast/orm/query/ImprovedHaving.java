package com.fast.orm.query;

import com.fast.orm.curd.condition.FieldCondition;
import com.fast.orm.curd.field.AggregateField;
import com.fast.orm.curd.field.SelectFnField;
import com.fast.orm.curd.having.HavingCondition;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 改进的Having条件处理
 * 支持类型安全和完整的操作集合
 */
public class ImprovedHaving<T> {
    
    private final AggregateField field;
    private final Class<T> valueType;

    private ImprovedHaving(AggregateField field, Class<T> valueType) {
        this.field = field;
        this.valueType = valueType;
    }

    // 基础比较操作
    public HavingCondition eq(T value) {
        return createHavingCondition("eq", value);
    }

    public HavingCondition neq(T value) {
        return createHavingCondition("neq", value);
    }

    public HavingCondition gt(T value) {
        return createHavingCondition("gt", value);
    }

    public HavingCondition gte(T value) {
        return createHavingCondition("gte", value);
    }

    public HavingCondition lt(T value) {
        return createHavingCondition("lt", value);
    }

    public HavingCondition lte(T value) {
        return createHavingCondition("lte", value);
    }

    public HavingCondition between(T start, T end) {
        return createHavingCondition("between", List.of(start, end));
    }

    public HavingCondition in(List<T> values) {
        return createHavingCondition("in", values);
    }

    public HavingCondition notIn(List<T> values) {
        return createHavingCondition("nin", values);
    }

    public HavingCondition isNull() {
        return createHavingCondition("isNull", true);
    }

    public HavingCondition isNotNull() {
        return createHavingCondition("isNull", false);
    }

    // 私有方法：创建Having条件
    private HavingCondition createHavingCondition(String operation, Object value) {
        FieldCondition.FieldConditionBuilder builder = FieldCondition.builder();
        
        FieldCondition fieldCondition = switch (operation) {
            case "eq" -> builder.eq(value).build();
            case "neq" -> builder.neq(value).build();
            case "gt" -> builder.gt(value).build();
            case "gte" -> builder.gte(value).build();
            case "lt" -> builder.lt(value).build();
            case "lte" -> builder.lte(value).build();
            case "between" -> builder.between((List<Object>) value).build();
            case "in" -> builder.in((List<Object>) value).build();
            case "nin" -> builder.nin((List<Object>) value).build();
            case "isNull" -> builder.isNull((Boolean) value).build();
            default -> throw new UnsupportedOperationException("Unsupported operation: " + operation);
        };

        return HavingCondition.builder()
            .field(field)
            .condition(fieldCondition)
            .build();
    }

    // 静态工厂方法 - 类型安全的创建方式
    public static ImprovedHaving<Integer> havingInt(SelectFnField field) {
        return new ImprovedHaving<>(field.getParam(), Integer.class);
    }

    public static ImprovedHaving<Long> havingLong(SelectFnField field) {
        return new ImprovedHaving<>(field.getParam(), Long.class);
    }

    public static ImprovedHaving<BigDecimal> havingBigDecimal(SelectFnField field) {
        return new ImprovedHaving<>(field.getParam(), BigDecimal.class);
    }

    public static ImprovedHaving<String> havingString(SelectFnField field) {
        return new ImprovedHaving<>(field.getParam(), String.class);
    }

    public static ImprovedHaving<LocalDate> havingDate(SelectFnField field) {
        return new ImprovedHaving<>(field.getParam(), LocalDate.class);
    }

    public static ImprovedHaving<LocalDateTime> havingDateTime(SelectFnField field) {
        return new ImprovedHaving<>(field.getParam(), LocalDateTime.class);
    }

    // 通用方法（向后兼容）
    public static ImprovedHaving<Object> having(SelectFnField field) {
        return new ImprovedHaving<>(field.getParam(), Object.class);
    }

    // 链式操作支持
    public HavingCondition and(HavingCondition other) {
        return HavingCondition.builder()
            .and(List.of(
                HavingCondition.builder().field(this.field).build(),
                other
            ))
            .build();
    }

    public HavingCondition or(HavingCondition other) {
        return HavingCondition.builder()
            .or(List.of(
                HavingCondition.builder().field(this.field).build(),
                other
            ))
            .build();
    }
}
