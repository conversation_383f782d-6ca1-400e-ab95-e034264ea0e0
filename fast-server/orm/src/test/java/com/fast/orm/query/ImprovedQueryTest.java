package com.fast.orm.query;

import com.fast.core.basic.entity.data.TableInfo;
import com.fast.core.data.TableMeta;
import com.fast.core.data.utils.EntityMetadataParser;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;

import static com.fast.orm.query.ImprovedConditions.*;

/**
 * 改进后的查询测试
 * 展示更好的having和temp条件处理方式
 */
public class ImprovedQueryTest {

    @Test
    public void testImprovedQuery() {
        TableMeta tableMeta = EntityMetadataParser.parseEntity(TableInfo.class);
        TypeTableInfo TABLE_INFO = TypeTableInfo.$;
        Query query = new Query();

        com.fast.orm.curd.query.Query queryBuild = query.form(tableMeta)
            .select(TABLE_INFO.id(), TABLE_INFO.name(), TABLE_INFO.cnName())
            .where(
                TABLE_INFO.id().eq("123")
                    .and(TABLE_INFO.name().like("%xxx"))
            )
            // ✅ 改进后：Having条件支持完整操作集合
            .having(having(TABLE_INFO.id().count()).gt(1L))  // COUNT通常返回Long
            // ✅ 改进后：Temp条件类型安全
            .temp(tempInt("user_count").between(10, 100))     // 明确指定为整数类型
            .orderBy(TABLE_INFO.id().desc())
            .limit(0, 10)
            .build();

        queryBuild.compile();
        String sql = queryBuild.getSql();
        Map<String, Object> params = queryBuild.getParams();
        
        System.out.println("=== 改进后的查询 ===");
        System.out.println("SQL: " + sql);
        params.forEach((k, v) -> System.out.println(k + ": " + v));
    }

    @Test
    public void testHavingConditionsDemo() {
        TypeTableInfo TABLE_INFO = TypeTableInfo.$;
        
        System.out.println("=== Having条件改进演示 ===");
        
        // ✅ 原来只有gt方法，现在支持完整操作
        var countGt = having(TABLE_INFO.id().count()).gt(1L);
        var countEq = having(TABLE_INFO.id().count()).eq(5L);
        var countBetween = having(TABLE_INFO.id().count()).between(1L, 10L);
        var countIn = having(TABLE_INFO.id().count()).in(Arrays.asList(1L, 2L, 3L));
        var countIsNull = having(TABLE_INFO.id().count()).isNull();
        
        // ✅ 类型安全的Having条件
        var avgSalary = havingDecimal(TABLE_INFO.id().count()).gt(new BigDecimal("5000"));
        var maxAge = havingInt(TABLE_INFO.id().count()).lte(65);
        
        System.out.println("Having条件现在支持: eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull");
    }

    @Test
    public void testTempConditionsDemo() {
        System.out.println("=== Temp条件改进演示 ===");
        
        // ✅ 类型安全的Temp条件
        var stringTemp = tempString("user_name").like("%admin%");  // 字符串类型，支持like
        var intTemp = tempInt("age_group").between(18, 65);        // 整数类型，支持比较
        var decimalTemp = tempDecimal("avg_salary").gt(new BigDecimal("8000"));
        var dateTemp = tempDateTime("last_login").gte(LocalDateTime.now().minusDays(30));
        var booleanTemp = tempBoolean("is_active").eq(true);
        
        // ✅ 完整的操作支持
        var temp1 = tempInt("score").eq(100);
        var temp2 = tempInt("score").neq(0);
        var temp3 = tempInt("score").gt(80);
        var temp4 = tempInt("score").between(60, 100);
        var temp5 = tempInt("score").in(Arrays.asList(90, 95, 100));
        var temp6 = tempInt("score").isNotNull();
        
        System.out.println("Temp条件现在支持:");
        System.out.println("- 类型安全: tempString, tempInt, tempDecimal, tempDate, tempBoolean");
        System.out.println("- 完整操作: eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull");
        System.out.println("- 字符串特有: like, nlike (仅tempString支持)");
    }

    @Test
    public void testTypeSafetyDemo() {
        System.out.println("=== 类型安全演示 ===");
        
        // ✅ 类型安全 - 编译时检查
        var stringTemp = tempString("name");
        var stringLike = stringTemp.like("%admin%");     // ✓ 字符串支持like
        var stringEq = stringTemp.eq("admin");           // ✓ 字符串支持eq
        
        var intTemp = tempInt("age");
        var intGt = intTemp.gt(18);                      // ✓ 整数支持比较
        var intBetween = intTemp.between(18, 65);        // ✓ 整数支持范围
        // var intLike = intTemp.like("%18%");           // ✗ 编译错误！整数不支持like
        
        var decimalTemp = tempDecimal("salary");
        var decimalGt = decimalTemp.gt(new BigDecimal("5000"));  // ✓ 类型匹配
        // var decimalGt2 = decimalTemp.gt("5000");              // ✗ 编译错误！类型不匹配
        
        System.out.println("✅ 类型安全检查通过");
    }

    @Test
    public void testBackwardCompatibility() {
        System.out.println("=== 向后兼容性演示 ===");
        
        TypeTableInfo TABLE_INFO = TypeTableInfo.$;
        
        // ✅ 保持向后兼容
        var oldStyleHaving = having(TABLE_INFO.id().count()).gt(1L);  // 原有方式仍然可用
        var oldStyleTemp = temp("test").gt(2);                        // 原有方式仍然可用
        
        // ✅ 新的类型安全方式
        var newStyleHaving = havingLong(TABLE_INFO.id().count()).gt(1L);
        var newStyleTemp = tempInt("test").gt(2);
        
        System.out.println("✅ 向后兼容性保持，同时提供更好的类型安全选项");
    }

    @Test
    public void testComplexQuery() {
        TableMeta tableMeta = EntityMetadataParser.parseEntity(TableInfo.class);
        TypeTableInfo TABLE_INFO = TypeTableInfo.$;
        Query query = new Query();

        // 复杂查询示例
        com.fast.orm.curd.query.Query complexQuery = query.form(tableMeta)
            .select(TABLE_INFO.id(), TABLE_INFO.name(), TABLE_INFO.cnName())
            .where(
                TABLE_INFO.name().like("%user%")
                    .and(TABLE_INFO.id().isNotNull())
            )
            // 复杂的Having条件
            .having(
                havingLong(TABLE_INFO.id().count()).between(5L, 50L)
            )
            // 复杂的Temp条件
            .temp(
                tempDecimal("avg_score").gte(new BigDecimal("75.5"))
            )
            .orderBy(TABLE_INFO.name().asc())
            .limit(1, 20)
            .build();

        complexQuery.compile();
        System.out.println("=== 复杂查询示例 ===");
        System.out.println("SQL: " + complexQuery.getSql());
        complexQuery.getParams().forEach((k, v) -> System.out.println(k + ": " + v));
    }
}
