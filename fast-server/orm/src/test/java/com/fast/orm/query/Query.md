
Field query = Field.newField("getUser")
.argument(Argument.newArgument().name("id").value("123"))
.selectionSet(SelectionSet.newSelectionSet()
.field("id")
.field("name")
.build())
.build();
String graphqlQuery = Operation.operation(QUERY, query).build().toString();

UserQuery query = UserQuery.builder()
.id("123")
.select(UserResponseProjection().id().name());
String graphqlQuery = query.build();



UserQuery query = UserQuery.builder()
.all()
.select(UserTable.id().name().userName())
.where()
.id().eq("xx")
.name().like("%xxx")
.orderBy(UserTable.id().asc())
.build();
String graphqlQuery = query.build();


Query _userAll {
  _userAll(
    searchCondition: {
      id: {
        eq: "123"
      },
      name: {
        like: "%xxx"
      }
    },
      sort: {
        id: ASC
      },
) {
    id
    name
    username
  }
}
