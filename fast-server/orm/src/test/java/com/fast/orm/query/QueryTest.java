package com.fast.orm.query;

import com.fast.core.basic.entity.data.TableInfo;
import com.fast.core.data.TableMeta;
import com.fast.core.data.utils.EntityMetadataParser;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.fast.orm.query.Having.having;
import static com.fast.orm.query.Temp.temp;


public class QueryTest {

  @Test
  public void createQuery() {

    TableMeta tableMeta = EntityMetadataParser.parseEntity(TableInfo.class);
    TypeTableInfo TABLE_INFO = TypeTableInfo.$;
    Query query = new Query();

    com.fast.orm.curd.query.Query queryBuild = query.form(tableMeta)
      .select(TABLE_INFO.id(), TABLE_INFO.name(), TABLE_INFO.cnName())
      .where(
        TABLE_INFO.id().eq("123")
          .and(TABLE_INFO.name().like("%xxx"))
      )
      .having(having(TABLE_INFO.id().count()).gt(1))
      .temp(temp("test").gt(2))
      .orderBy(TABLE_INFO.id().desc())
      .limit(0, 10)
      .build();


    queryBuild.compile();
    String sql = queryBuild.getSql();
    Map<String, Object> params = queryBuild.getParams();
    System.out.println("sql: " + sql);
    // 打印params
    params.forEach((k, v) -> {
      System.out.println(k + ": " + v);
    });
  }
}
