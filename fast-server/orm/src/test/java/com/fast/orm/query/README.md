# Field类设计改进方案

## 问题描述

原有的Field类存在以下问题：
1. 只支持String类型的条件方法（eq, neq, like, nlike）
2. 缺少数字类型的比较方法（gt, gte, lt, lte等）
3. 没有根据字段类型提供相应的条件方法
4. 类型安全性不足

## 解决方案

采用**泛型化Field类**的设计方案，主要改进包括：

### 1. 泛型化设计

```java
public class Field<T> {
    private SelectField selectField;
    private BasicDataType dataType;
    
    // 构造方法
    public Field(SelectField selectField, BasicDataType dataType) {
        this.selectField = selectField;
        this.dataType = dataType;
    }
}
```

### 2. 类型安全的条件方法

#### 通用条件方法（所有类型支持）
- `eq(T value)` - 等于
- `neq(T value)` - 不等于
- `isNull()` - 为空
- `isNotNull()` - 不为空
- `in(List<T> values)` - 在列表中
- `notIn(List<T> values)` - 不在列表中

#### 字符串特有方法
- `like(String value)` - 模糊匹配
- `nlike(String value)` - 不匹配

#### 可比较类型方法（数字、日期）
- `gt(T value)` - 大于
- `gte(T value)` - 大于等于
- `lt(T value)` - 小于
- `lte(T value)` - 小于等于
- `between(T start, T end)` - 范围查询

### 3. 静态工厂方法

提供便捷的字段创建方法：

```java
// 字符串字段
Field<String> nameField = Field.stringField("name");

// 整数字段
Field<Integer> ageField = Field.intField("age");

// 大数字段
Field<BigDecimal> salaryField = Field.bigDecimalField("salary");

// 日期字段
Field<LocalDate> birthDateField = Field.dateField("birth_date");

// 日期时间字段
Field<LocalDateTime> createTimeField = Field.dateTimeField("create_time");

// 布尔字段
Field<Boolean> activeField = Field.booleanField("active");
```

### 4. 运行时类型检查

在方法调用时进行类型检查，确保操作的合法性：

```java
// 字符串字段可以使用like
Field<String> nameField = Field.stringField("name");
nameField.like("%张%"); // ✓ 正确

// 数字字段不能使用like
Field<Integer> ageField = Field.intField("age");
// ageField.like("25"); // ✗ 编译错误

// 数字字段可以使用比较操作
ageField.gt(18); // ✓ 正确
ageField.between(18, 65); // ✓ 正确
```

## 支持的数据类型

| 数据类型 | Java类型 | 支持的操作 |
|---------|---------|-----------|
| FIELD_STRING | String | eq, neq, like, nlike, in, notIn, isNull, isNotNull |
| FIELD_INTEGER | Integer | eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull |
| FIELD_BIG_DECIMAL | BigDecimal | eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull |
| FIELD_DATE | LocalDate | eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull |
| FIELD_DATE_TIME | LocalDateTime | eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull |
| FIELD_BOOLEAN | Boolean | eq, neq, isNull, isNotNull |

## 使用示例

### 基本使用

```java
// 创建字段
Field<String> nameField = Field.stringField("name");
Field<Integer> ageField = Field.intField("age");

// 字符串条件
BaseTypeCondition nameCondition = nameField.like("%张%");
BaseTypeCondition nameEqCondition = nameField.eq("张三");

// 数字条件
BaseTypeCondition ageCondition = ageField.between(18, 65);
BaseTypeCondition ageGtCondition = ageField.gt(25);

// 排序
Order nameAsc = nameField.asc();
Order ageDesc = ageField.desc();
```

### 在UserTable中使用

```java
public class UserTable {
    public static final UserTable $ = new UserTable();

    public Field<String> name() {
        SelectField selectField = SelectField.builder().name("name").build();
        return new Field<>(selectField, BasicDataType.FIELD_STRING);
    }

    public Field<Integer> age() {
        SelectField selectField = SelectField.builder().name("age").build();
        return new Field<>(selectField, BasicDataType.FIELD_INTEGER);
    }
}

// 使用
UserTable userTable = UserTable.$;
BaseTypeCondition condition = userTable.name().like("%张%")
    .and(userTable.age().between(18, 65));
```

## 优势

1. **类型安全**：编译时检查类型匹配，避免运行时错误
2. **操作明确**：不同类型字段提供相应的操作方法
3. **易于扩展**：新增数据类型只需添加对应的条件类和工厂方法
4. **向后兼容**：保持原有API的基本结构
5. **代码清晰**：通过类型明确表达字段的数据类型和支持的操作

## 注意事项

1. 需要在创建Field时指定正确的数据类型
2. 不支持的操作会在运行时抛出异常
3. 建议使用静态工厂方法创建Field实例
4. 在Table类中需要为每个字段指定正确的类型
