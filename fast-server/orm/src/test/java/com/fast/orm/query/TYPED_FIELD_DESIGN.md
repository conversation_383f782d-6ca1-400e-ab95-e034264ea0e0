# 类型化Field设计方案

## 设计目标

实现你要求的效果：**Field<Number> 类型点不出来 .like() 方法，IDE在编写代码时就能识别出错误，而不需要等到编译时。**

## 核心设计思想

通过**接口分离**和**具体类型实现**，让IDE能够在代码编写阶段就提供精确的方法提示，避免不支持的方法出现在自动完成列表中。

## 设计架构

### 1. 接口分离 (FieldOperations.java)

```java
// 基础操作 - 所有字段都支持
interface BaseOperations<T> {
    BaseTypeCondition eq(T value);
    BaseTypeCondition neq(T value);
    // ...
}

// 字符串操作 - 只有字符串字段支持
interface StringOperations {
    BaseTypeCondition like(String value);
    BaseTypeCondition nlike(String value);
}

// 比较操作 - 数字和日期字段支持
interface ComparableOperations<T> {
    BaseTypeCondition gt(T value);
    BaseTypeCondition gte(T value);
    // ...
}

// 组合接口
interface StringField<T> extends BaseOperations<T>, StringOperations {}
interface NumberField<T> extends BaseOperations<T>, ComparableOperations<T> {}
interface BooleanField<T> extends BaseOperations<T> {}
```

### 2. 具体实现类 (TypedField.java)

```java
// 字符串字段 - 实现StringField接口
public static class StringField extends TypedField<String> 
    implements FieldOperations.StringField<String> {
    // 实现所有字符串相关方法
}

// 整数字段 - 实现NumberField接口
public static class IntegerField extends TypedField<Integer> 
    implements FieldOperations.NumberField<Integer> {
    // 实现所有数字相关方法，没有like方法
}

// 布尔字段 - 只实现BooleanField接口
public static class BooleanField extends TypedField<Boolean> 
    implements FieldOperations.BooleanField<Boolean> {
    // 只有基础方法，没有like和比较方法
}
```

### 3. 类型化表定义 (TypedUserTable.java)

```java
public class TypedUserTable {
    // 返回具体的字段类型
    public TypedField.StringField name() { ... }      // 有like方法
    public TypedField.IntegerField age() { ... }      // 有比较方法，无like
    public TypedField.BooleanField active() { ... }   // 只有基础方法
}
```

## IDE智能提示效果

### ✅ 字符串字段 (StringField)
```java
TypedUserTable.$.name().  // IDE提示：
// ✓ eq(String)
// ✓ neq(String) 
// ✓ like(String)     ← 字符串特有
// ✓ nlike(String)    ← 字符串特有
// ✓ in(List<String>)
// ✓ isNull()
// ✓ asc()
// ✓ desc()
```

### ✅ 数字字段 (IntegerField)
```java
TypedUserTable.$.age().   // IDE提示：
// ✓ eq(Integer)
// ✓ neq(Integer)
// ✓ gt(Integer)      ← 数字特有
// ✓ gte(Integer)     ← 数字特有
// ✓ lt(Integer)      ← 数字特有
// ✓ lte(Integer)     ← 数字特有
// ✓ between(Integer, Integer) ← 数字特有
// ✓ in(List<Integer>)
// ✓ isNull()
// ✓ asc()
// ✓ desc()
// ✗ 没有 like() 方法！
```

### ✅ 布尔字段 (BooleanField)
```java
TypedUserTable.$.active(). // IDE提示：
// ✓ eq(Boolean)
// ✓ neq(Boolean)
// ✓ in(List<Boolean>)
// ✓ isNull()
// ✓ asc()
// ✓ desc()
// ✗ 没有 like() 方法！
// ✗ 没有 gt(), gte(), lt(), lte(), between() 方法！
```

## 使用示例

### 正确的使用方式
```java
TypedUserTable userTable = TypedUserTable.$;

// ✓ 字符串字段可以使用like
BaseTypeCondition nameCondition = userTable.name().like("%张%");

// ✓ 数字字段可以使用比较操作
BaseTypeCondition ageCondition = userTable.age().gt(18);
BaseTypeCondition salaryCondition = userTable.salary().between(
    new BigDecimal("3000"), 
    new BigDecimal("10000")
);

// ✓ 布尔字段只能使用基础操作
BaseTypeCondition activeCondition = userTable.active().eq(true);
```

### IDE会阻止的错误使用
```java
// ✗ IDE不会提示like方法，如果强行写会编译错误
// userTable.age().like("25");        // 编译错误
// userTable.salary().nlike("5000");  // 编译错误

// ✗ IDE不会提示比较方法，如果强行写会编译错误  
// userTable.active().gt(true);       // 编译错误
// userTable.name().between("a", "z"); // 编译错误
```

## 技术优势

### 1. **编译时类型安全**
- 不支持的操作在编译时就会报错
- 避免运行时异常

### 2. **IDE智能提示精确**
- 只显示当前字段类型支持的方法
- 提高开发效率，减少错误

### 3. **代码可读性强**
- 通过类型明确表达字段的数据类型
- 代码意图清晰

### 4. **易于维护和扩展**
- 新增字段类型只需实现相应接口
- 接口分离使得职责清晰

## 支持的字段类型和操作

| 字段类型 | 基础操作 | 字符串操作 | 比较操作 | 说明 |
|---------|---------|-----------|---------|------|
| StringField | ✓ | ✓ | ✗ | 支持like/nlike |
| IntegerField | ✓ | ✗ | ✓ | 支持数字比较 |
| BigDecimalField | ✓ | ✗ | ✓ | 支持数字比较 |
| DateField | ✓ | ✗ | ✓ | 支持日期比较 |
| DateTimeField | ✓ | ✗ | ✓ | 支持日期时间比较 |
| BooleanField | ✓ | ✗ | ✗ | 只支持基础操作 |

**基础操作**: eq, neq, in, notIn, isNull, isNotNull, asc, desc
**字符串操作**: like, nlike  
**比较操作**: gt, gte, lt, lte, between

## 迁移建议

1. **保留原有Field类**：用于向后兼容
2. **新代码使用TypedField**：获得更好的类型安全性
3. **逐步迁移**：可以在同一个项目中混用两种方式

这个设计完美实现了你的需求：**IDE在编写代码时就能识别Field<Number>类型点不出来.like()方法**，提供了最佳的开发体验！
