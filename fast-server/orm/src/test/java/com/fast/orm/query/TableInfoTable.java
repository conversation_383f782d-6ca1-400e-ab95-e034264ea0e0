package com.fast.orm.query;

import com.fast.orm.curd.field.SelectField;

public class TableInfoTable {

  public static final TableInfoTable $ = new TableInfoTable();


  public Field id() {
    SelectField selectField = SelectField.builder()
      .name("id")
      .build();
    return new Field(selectField);
  }

  public Field name() {
    SelectField selectField = SelectField.builder()
      .name("name")
      .build();
    return new Field(selectField);
  }

  public Field cnname() {
    SelectField selectField = SelectField.builder()
      .name("cnname")
      .build();
    return new Field(selectField);
  }

}
