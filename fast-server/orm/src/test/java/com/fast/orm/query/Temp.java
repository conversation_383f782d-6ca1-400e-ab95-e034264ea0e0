package com.fast.orm.query;

import com.fast.orm.curd.condition.FieldCondition;
import com.fast.orm.curd.temp.TempCondition;

public class Temp {

  private final String field;

  public Temp(String field) {
    this.field = field;
  }

  public TempCondition gt(Object value) {
    FieldCondition fieldCondition = FieldCondition.builder()
      .gt(value)
      .build();
    return TempCondition.builder()
      .alias(field)
      .condition(fieldCondition)
      .build();
  }}
