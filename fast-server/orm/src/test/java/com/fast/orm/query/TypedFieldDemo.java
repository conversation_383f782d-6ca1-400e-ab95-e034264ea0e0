package com.fast.orm.query;

import com.fast.orm.curd.condition.BaseTypeCondition;
import com.fast.orm.curd.page.Order;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 类型化字段演示
 * 展示IDE智能提示的效果
 */
public class TypedFieldDemo {

    public static void main(String[] args) {
        demonstrateStringField();
        demonstrateNumberField();
        demonstrateDateField();
        demonstrateBooleanField();
        demonstrateUserTable();
    }

    /**
     * 字符串字段演示
     * IDE会提示：eq, neq, like, nlike, in, notIn, isNull, isNotNull, asc, desc
     */
    private static void demonstrateStringField() {
        System.out.println("=== 字符串字段演示 ===");
        
        TypedField.StringField nameField = new TypedField.StringField(
            com.fast.orm.curd.field.SelectField.builder().name("name").build()
        );
        
        // 字符串字段支持的所有操作
        BaseTypeCondition eq = nameField.eq("张三");
        BaseTypeCondition neq = nameField.neq("李四");
        BaseTypeCondition like = nameField.like("%张%");        // ✓ 字符串特有
        BaseTypeCondition nlike = nameField.nlike("%李%");      // ✓ 字符串特有
        BaseTypeCondition in = nameField.in(Arrays.asList("张三", "李四"));
        BaseTypeCondition notIn = nameField.notIn(Arrays.asList("王五"));
        BaseTypeCondition isNull = nameField.isNull();
        BaseTypeCondition isNotNull = nameField.isNotNull();
        
        Order asc = nameField.asc();
        Order desc = nameField.desc();
        
        // 注意：IDE不会提示 gt(), gte(), lt(), lte(), between() 方法
        // nameField.gt("张三");  // ✗ 编译错误，字符串字段没有这些方法
        
        System.out.println("字符串字段支持: eq, neq, like, nlike, in, notIn, isNull, isNotNull, asc, desc");
    }

    /**
     * 数字字段演示
     * IDE会提示：eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull, asc, desc
     * IDE不会提示：like, nlike
     */
    private static void demonstrateNumberField() {
        System.out.println("=== 数字字段演示 ===");
        
        TypedField.IntegerField ageField = new TypedField.IntegerField(
            com.fast.orm.curd.field.SelectField.builder().name("age").build()
        );
        
        TypedField.BigDecimalField salaryField = new TypedField.BigDecimalField(
            com.fast.orm.curd.field.SelectField.builder().name("salary").build()
        );
        
        // 数字字段支持的所有操作
        BaseTypeCondition eq = ageField.eq(25);
        BaseTypeCondition neq = ageField.neq(30);
        BaseTypeCondition gt = ageField.gt(18);                 // ✓ 数字特有
        BaseTypeCondition gte = ageField.gte(18);               // ✓ 数字特有
        BaseTypeCondition lt = ageField.lt(65);                 // ✓ 数字特有
        BaseTypeCondition lte = ageField.lte(65);               // ✓ 数字特有
        BaseTypeCondition between = ageField.between(18, 65);   // ✓ 数字特有
        BaseTypeCondition in = ageField.in(Arrays.asList(18, 25, 30));
        BaseTypeCondition notIn = ageField.notIn(Arrays.asList(0));
        BaseTypeCondition isNull = ageField.isNull();
        BaseTypeCondition isNotNull = ageField.isNotNull();
        
        Order asc = ageField.asc();
        Order desc = ageField.desc();
        
        // BigDecimal字段同样支持比较操作
        BaseTypeCondition salaryGt = salaryField.gt(new BigDecimal("5000"));
        BaseTypeCondition salaryBetween = salaryField.between(
            new BigDecimal("3000"), 
            new BigDecimal("10000")
        );
        
        // 注意：IDE不会提示 like(), nlike() 方法
        // ageField.like("25");     // ✗ 编译错误，数字字段没有这些方法
        // salaryField.nlike("5000"); // ✗ 编译错误，数字字段没有这些方法
        
        System.out.println("数字字段支持: eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull, asc, desc");
        System.out.println("数字字段不支持: like, nlike");
    }

    /**
     * 日期字段演示
     * IDE会提示：eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull, asc, desc
     * IDE不会提示：like, nlike
     */
    private static void demonstrateDateField() {
        System.out.println("=== 日期字段演示 ===");
        
        TypedField.DateField birthDateField = new TypedField.DateField(
            com.fast.orm.curd.field.SelectField.builder().name("birth_date").build()
        );
        
        TypedField.DateTimeField createTimeField = new TypedField.DateTimeField(
            com.fast.orm.curd.field.SelectField.builder().name("create_time").build()
        );
        
        LocalDate today = LocalDate.now();
        LocalDate lastYear = today.minusYears(1);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastMonth = now.minusMonths(1);
        
        // 日期字段支持的所有操作
        BaseTypeCondition eq = birthDateField.eq(today);
        BaseTypeCondition gt = birthDateField.gt(lastYear);     // ✓ 日期比较
        BaseTypeCondition between = birthDateField.between(lastYear, today); // ✓ 日期范围
        
        BaseTypeCondition timeGte = createTimeField.gte(lastMonth); // ✓ 日期时间比较
        BaseTypeCondition timeLt = createTimeField.lt(now);
        
        // 注意：IDE不会提示 like(), nlike() 方法
        // birthDateField.like("2023"); // ✗ 编译错误，日期字段没有这些方法
        
        System.out.println("日期字段支持: eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull, asc, desc");
        System.out.println("日期字段不支持: like, nlike");
    }

    /**
     * 布尔字段演示
     * IDE会提示：eq, neq, in, notIn, isNull, isNotNull, asc, desc
     * IDE不会提示：like, nlike, gt, gte, lt, lte, between
     */
    private static void demonstrateBooleanField() {
        System.out.println("=== 布尔字段演示 ===");
        
        TypedField.BooleanField activeField = new TypedField.BooleanField(
            com.fast.orm.curd.field.SelectField.builder().name("active").build()
        );
        
        // 布尔字段支持的操作
        BaseTypeCondition eq = activeField.eq(true);
        BaseTypeCondition neq = activeField.neq(false);
        BaseTypeCondition in = activeField.in(Arrays.asList(true, false));
        BaseTypeCondition notIn = activeField.notIn(Arrays.asList(false));
        BaseTypeCondition isNull = activeField.isNull();
        BaseTypeCondition isNotNull = activeField.isNotNull();
        
        Order asc = activeField.asc();
        Order desc = activeField.desc();
        
        // 注意：IDE不会提示比较方法和字符串方法
        // activeField.gt(true);        // ✗ 编译错误，布尔字段没有比较方法
        // activeField.like("true");    // ✗ 编译错误，布尔字段没有字符串方法
        // activeField.between(false, true); // ✗ 编译错误，布尔字段没有范围方法
        
        System.out.println("布尔字段支持: eq, neq, in, notIn, isNull, isNotNull, asc, desc");
        System.out.println("布尔字段不支持: like, nlike, gt, gte, lt, lte, between");
    }

    /**
     * 使用TypedUserTable演示
     * 展示在实际使用中的IDE智能提示效果
     */
    private static void demonstrateUserTable() {
        System.out.println("=== TypedUserTable使用演示 ===");
        
        TypedUserTable userTable = TypedUserTable.$;
        
        // 字符串字段 - IDE会提示like方法
        BaseTypeCondition nameCondition = userTable.name().like("%张%");
        BaseTypeCondition usernameCondition = userTable.username().eq("zhangsan");
        
        // 数字字段 - IDE会提示比较方法，不会提示like方法
        BaseTypeCondition ageCondition = userTable.age().between(18, 65);
        BaseTypeCondition salaryCondition = userTable.salary().gt(new BigDecimal("5000"));
        
        // 日期字段 - IDE会提示比较方法，不会提示like方法
        BaseTypeCondition birthCondition = userTable.birthDate().gt(LocalDate.now().minusYears(30));
        BaseTypeCondition createCondition = userTable.createTime().gte(LocalDateTime.now().minusMonths(1));
        
        // 布尔字段 - IDE只会提示基础方法，不会提示like和比较方法
        BaseTypeCondition activeCondition = userTable.active().eq(true);
        
        // 排序
        Order nameAsc = userTable.name().asc();
        Order ageDesc = userTable.age().desc();
        
        System.out.println("TypedUserTable根据字段类型提供精确的方法提示");
        System.out.println("- 字符串字段: 有like方法");
        System.out.println("- 数字字段: 有比较方法，无like方法");
        System.out.println("- 日期字段: 有比较方法，无like方法");
        System.out.println("- 布尔字段: 无like方法，无比较方法");
    }
}
