package com.fast.orm.query;

import com.fast.orm.curd.condition.BaseTypeCondition;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.curd.page.Order;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 类型化字段的单元测试
 * 验证不同类型字段的方法可用性
 */
public class TypedFieldTest {

    @Test
    public void testStringFieldOperations() {
        SelectField selectField = SelectField.builder().name("name").build();
        TypedField.StringField stringField = new TypedField.StringField(selectField);
        
        // 测试字符串特有的操作
        BaseTypeCondition likeCondition = stringField.like("%test%");
        assertNotNull(likeCondition);
        
        BaseTypeCondition nlikeCondition = stringField.nlike("%test%");
        assertNotNull(nlikeCondition);
        
        // 测试基础操作
        BaseTypeCondition eqCondition = stringField.eq("test");
        assertNotNull(eqCondition);
        
        BaseTypeCondition neqCondition = stringField.neq("test");
        assertNotNull(neqCondition);
        
        BaseTypeCondition inCondition = stringField.in(Arrays.asList("test1", "test2"));
        assertNotNull(inCondition);
        
        BaseTypeCondition notInCondition = stringField.notIn(Arrays.asList("test3"));
        assertNotNull(notInCondition);
        
        BaseTypeCondition isNullCondition = stringField.isNull();
        assertNotNull(isNullCondition);
        
        BaseTypeCondition isNotNullCondition = stringField.isNotNull();
        assertNotNull(isNotNullCondition);
        
        // 测试排序
        Order ascOrder = stringField.asc();
        assertNotNull(ascOrder);
        assertEquals(Order.Direction.ASC, ascOrder.getDirection());
        
        Order descOrder = stringField.desc();
        assertNotNull(descOrder);
        assertEquals(Order.Direction.DESC, descOrder.getDirection());
    }

    @Test
    public void testIntegerFieldOperations() {
        SelectField selectField = SelectField.builder().name("age").build();
        TypedField.IntegerField integerField = new TypedField.IntegerField(selectField);
        
        // 测试数字比较操作
        BaseTypeCondition gtCondition = integerField.gt(18);
        assertNotNull(gtCondition);
        
        BaseTypeCondition gteCondition = integerField.gte(18);
        assertNotNull(gteCondition);
        
        BaseTypeCondition ltCondition = integerField.lt(65);
        assertNotNull(ltCondition);
        
        BaseTypeCondition lteCondition = integerField.lte(65);
        assertNotNull(lteCondition);
        
        BaseTypeCondition betweenCondition = integerField.between(18, 65);
        assertNotNull(betweenCondition);
        
        // 测试基础操作
        BaseTypeCondition eqCondition = integerField.eq(25);
        assertNotNull(eqCondition);
        
        BaseTypeCondition inCondition = integerField.in(Arrays.asList(18, 25, 30));
        assertNotNull(inCondition);
        
        // 测试排序
        Order ascOrder = integerField.asc();
        assertNotNull(ascOrder);
        
        Order descOrder = integerField.desc();
        assertNotNull(descOrder);
    }

    @Test
    public void testBigDecimalFieldOperations() {
        SelectField selectField = SelectField.builder().name("salary").build();
        TypedField.BigDecimalField bigDecimalField = new TypedField.BigDecimalField(selectField);
        
        BigDecimal minSalary = new BigDecimal("3000");
        BigDecimal maxSalary = new BigDecimal("10000");
        BigDecimal targetSalary = new BigDecimal("5000");
        
        // 测试大数比较操作
        BaseTypeCondition gtCondition = bigDecimalField.gt(minSalary);
        assertNotNull(gtCondition);
        
        BaseTypeCondition betweenCondition = bigDecimalField.between(minSalary, maxSalary);
        assertNotNull(betweenCondition);
        
        BaseTypeCondition eqCondition = bigDecimalField.eq(targetSalary);
        assertNotNull(eqCondition);
    }

    @Test
    public void testDateFieldOperations() {
        SelectField selectField = SelectField.builder().name("birth_date").build();
        TypedField.DateField dateField = new TypedField.DateField(selectField);
        
        LocalDate today = LocalDate.now();
        LocalDate lastYear = today.minusYears(1);
        
        // 测试日期比较操作
        BaseTypeCondition gtCondition = dateField.gt(lastYear);
        assertNotNull(gtCondition);
        
        BaseTypeCondition betweenCondition = dateField.between(lastYear, today);
        assertNotNull(betweenCondition);
        
        BaseTypeCondition eqCondition = dateField.eq(today);
        assertNotNull(eqCondition);
    }

    @Test
    public void testDateTimeFieldOperations() {
        SelectField selectField = SelectField.builder().name("create_time").build();
        TypedField.DateTimeField dateTimeField = new TypedField.DateTimeField(selectField);
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastMonth = now.minusMonths(1);
        
        // 测试日期时间比较操作
        BaseTypeCondition gteCondition = dateTimeField.gte(lastMonth);
        assertNotNull(gteCondition);
        
        BaseTypeCondition ltCondition = dateTimeField.lt(now);
        assertNotNull(ltCondition);
        
        BaseTypeCondition betweenCondition = dateTimeField.between(lastMonth, now);
        assertNotNull(betweenCondition);
    }

    @Test
    public void testBooleanFieldOperations() {
        SelectField selectField = SelectField.builder().name("active").build();
        TypedField.BooleanField booleanField = new TypedField.BooleanField(selectField);
        
        // 测试布尔操作
        BaseTypeCondition eqCondition = booleanField.eq(true);
        assertNotNull(eqCondition);
        
        BaseTypeCondition neqCondition = booleanField.neq(false);
        assertNotNull(neqCondition);
        
        BaseTypeCondition inCondition = booleanField.in(Arrays.asList(true, false));
        assertNotNull(inCondition);
        
        BaseTypeCondition isNullCondition = booleanField.isNull();
        assertNotNull(isNullCondition);
        
        // 测试排序
        Order ascOrder = booleanField.asc();
        assertNotNull(ascOrder);
        
        Order descOrder = booleanField.desc();
        assertNotNull(descOrder);
    }

    @Test
    public void testTypedUserTable() {
        TypedUserTable userTable = TypedUserTable.$;
        
        // 测试字符串字段
        BaseTypeCondition nameCondition = userTable.name().like("%张%");
        assertNotNull(nameCondition);
        
        BaseTypeCondition usernameCondition = userTable.username().eq("zhangsan");
        assertNotNull(usernameCondition);
        
        // 测试数字字段
        BaseTypeCondition ageCondition = userTable.age().between(18, 65);
        assertNotNull(ageCondition);
        
        BaseTypeCondition salaryCondition = userTable.salary().gt(new BigDecimal("5000"));
        assertNotNull(salaryCondition);
        
        // 测试日期字段
        BaseTypeCondition birthCondition = userTable.birthDate().gt(LocalDate.now().minusYears(30));
        assertNotNull(birthCondition);
        
        BaseTypeCondition createCondition = userTable.createTime().gte(LocalDateTime.now().minusMonths(1));
        assertNotNull(createCondition);
        
        // 测试布尔字段
        BaseTypeCondition activeCondition = userTable.active().eq(true);
        assertNotNull(activeCondition);
        
        // 测试排序
        Order nameAsc = userTable.name().asc();
        assertNotNull(nameAsc);
        
        Order ageDesc = userTable.age().desc();
        assertNotNull(ageDesc);
    }

    @Test
    public void testFieldChaining() {
        SelectField selectField = SelectField.builder().name("name").build();
        TypedField.StringField stringField = new TypedField.StringField(selectField);
        
        // 测试链式调用
        TypedField<String> aliasedField = stringField.as("user_name").group();
        assertNotNull(aliasedField);
        assertEquals("user_name", aliasedField.getField().getAlias());
        assertTrue(aliasedField.getField().isGroup());
    }

    @Test
    public void testFieldProperties() {
        SelectField selectField = SelectField.builder().name("test_field").build();
        TypedField.StringField stringField = new TypedField.StringField(selectField);
        
        assertEquals("test_field", stringField.getField().getName());
        assertEquals(com.fast.core.data.type.BasicDataType.FIELD_STRING, stringField.getDataType());
        
        TypedField.IntegerField integerField = new TypedField.IntegerField(selectField);
        assertEquals(com.fast.core.data.type.BasicDataType.FIELD_INTEGER, integerField.getDataType());
    }
}
