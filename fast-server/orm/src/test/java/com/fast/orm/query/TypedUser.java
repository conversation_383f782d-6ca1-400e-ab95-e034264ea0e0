package com.fast.orm.query;

import com.fast.orm.curd.field.SelectField;
import com.fast.orm.type.TypedField;

/**
 * 类型化的用户表定义
 * 使用具体的字段类型，确保IDE能正确提示可用方法
 */
public class TypedUser {

    public static final TypedUser $ = new TypedUser();

    /**
     * 字符串字段 - 支持 eq, neq, like, nlike, in, notIn, isNull, isNotNull
     */
    public TypedField.StringField id() {
        SelectField selectField = SelectField.builder()
            .name("id")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 字符串字段 - 支持 eq, neq, like, nlike, in, notIn, isNull, isNotNull
     */
    public TypedField.StringField name() {
        SelectField selectField = SelectField.builder()
            .name("name")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 字符串字段 - 支持 eq, neq, like, nlike, in, notIn, isNull, isNotNull
     */
    public TypedField.StringField username() {
        SelectField selectField = SelectField.builder()
            .name("username")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 整数字段 - 支持 eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull
     * 注意：IDE不会提示 like() 方法
     */
    public TypedField.IntegerField age() {
        SelectField selectField = SelectField.builder()
            .name("age")
            .build();
        return new TypedField.IntegerField(selectField);
    }

    /**
     * 大数字段 - 支持 eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull
     * 注意：IDE不会提示 like() 方法
     */
    public TypedField.BigDecimalField salary() {
        SelectField selectField = SelectField.builder()
            .name("salary")
            .build();
        return new TypedField.BigDecimalField(selectField);
    }

    /**
     * 日期字段 - 支持 eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull
     * 注意：IDE不会提示 like() 方法
     */
    public TypedField.DateField birthDate() {
        SelectField selectField = SelectField.builder()
            .name("birth_date")
            .build();
        return new TypedField.DateField(selectField);
    }

    /**
     * 日期时间字段 - 支持 eq, neq, gt, gte, lt, lte, between, in, notIn, isNull, isNotNull
     * 注意：IDE不会提示 like() 方法
     */
    public TypedField.DateTimeField createTime() {
        SelectField selectField = SelectField.builder()
            .name("create_time")
            .build();
        return new TypedField.DateTimeField(selectField);
    }

    /**
     * 布尔字段 - 只支持 eq, neq, isNull, isNotNull, in, notIn
     * 注意：IDE不会提示 like(), gt(), gte(), lt(), lte(), between() 方法
     */
    public TypedField.BooleanField active() {
        SelectField selectField = SelectField.builder()
            .name("active")
            .build();
        return new TypedField.BooleanField(selectField);
    }
}
