package com.fast.orm.query;


import com.fast.core.data.TableMeta;
import com.fast.orm.curd.condition.BaseTypeCondition;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.curd.having.HavingCondition;
import com.fast.orm.curd.page.Order;
import com.fast.orm.curd.page.Pageable;
import com.fast.orm.curd.query.Query;
import com.fast.orm.curd.temp.TempCondition;

import java.util.ArrayList;
import java.util.List;

public class UserQuery {

  private QueryType queryType = QueryType.SINGLE;
  protected TableMeta tableMeta;
  protected Boolean distinct;
  protected List<SelectField> selectFields;
  protected BaseTypeCondition whereCondition;
  protected Boolean autoGroup;
  protected HavingCondition havingCondition;
  protected TempCondition tempCondition;

  // 私有构造，通过builder()创建
  private UserQuery(QueryType queryType) {
    this.queryType = queryType;
  }

  // 全选字段（如`all()`）
  public static UserQuery all() {
    return new UserQueryAll(QueryType.ALL); // 实际在build()中处理
  }

  public static UserQuery page() {
    return new UserQueryPage(QueryType.PAGE); // 实际在build()中处理
  }

  public static UserQuery single() {
    return new UserQuery(QueryType.SINGLE); // 实际在build()中处理
  }


  // 字段选择（链式调用）
  public UserQuerySelect select() {
    return new UserQuerySelect(this);
  }

  // 条件构造入口
  public UserQuery where() {
    this.whereCondition = new BaseTypeCondition();
    return this;
  }

  // 排序构造入口
  public UserQuery orderBy() {
    return this;
  }

  // 构建最终GraphQL查询字符串
  public Query build() {
      return Query.create(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition);
  }

  public static class UserQueryAll extends UserQuery {
    protected List<Order> orders;

    private UserQueryAll(QueryType queryType) {
      super(queryType);
    }

    public Query build() {
      return Query.create(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition, orders);
    }
  }

  public static class UserQueryPage extends UserQuery {
    protected Pageable pageable;

    private UserQueryPage(QueryType queryType) {
      super(queryType);
    }

    public Query build() {
      return Query.create(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition, pageable);
    }
  }


  public static class UserQuerySelect {
      private UserQuery userQuery;

      public UserQuerySelect(UserQuery userQuery) {
        this.userQuery = userQuery;
        this.userQuery.selectFields = new ArrayList<>();
      }


      public UserQuerySelect id(){
        SelectField selectField = SelectField.builder()
          .name("id")
          .build();
         this.userQuery.selectFields.add(selectField);
         return this;
      }

      public UserQuerySelect name(){
        SelectField selectField = SelectField.builder()
          .name("name")
          .build();
         this.userQuery.selectFields.add(selectField);
         return this;
      }

      public UserQuerySelect username(){
        SelectField selectField = SelectField.builder()
          .name("username")
          .build();
         this.userQuery.selectFields.add(selectField);
         return this;
      }

      public UserQuery where(){
        return this.userQuery.where();
      }


  }

}
