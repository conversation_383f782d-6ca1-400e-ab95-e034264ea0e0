package com.fast.orm.query;

import com.fast.core.data.type.BasicDataType;
import com.fast.orm.curd.field.SelectField;

public class UserTable {

  public static final UserTable $ = new UserTable();

  public Field<String> id() {
    SelectField selectField = SelectField.builder()
      .name("id")
      .build();
    return new Field<>(selectField, BasicDataType.FIELD_STRING);
  }

  public Field<String> name() {
    SelectField selectField = SelectField.builder()
      .name("name")
      .build();
    return new Field<>(selectField, BasicDataType.FIELD_STRING);
  }

  public Field<String> username() {
    SelectField selectField = SelectField.builder()
      .name("username")
      .build();
    return new Field<>(selectField, BasicDataType.FIELD_STRING);
  }

  // 示例：添加一些不同类型的字段
  public Field<Integer> age() {
    SelectField selectField = SelectField.builder()
      .name("age")
      .build();
    return new Field<>(selectField, BasicDataType.FIELD_INTEGER);
  }

  public Field<Boolean> active() {
    SelectField selectField = SelectField.builder()
      .name("active")
      .build();
    return new Field<>(selectField, BasicDataType.FIELD_BOOLEAN);
  }

}
