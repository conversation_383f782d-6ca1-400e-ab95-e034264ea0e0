package com.fast.orm.query;

import com.fast.orm.curd.field.SelectField;

public class UserTable {

  public static final UserTable $ = new UserTable();


  public Field id() {
    SelectField selectField = SelectField.builder()
      .name("id")
      .build();
    return new Field(selectField);
  }

  public Field name() {
    SelectField selectField = SelectField.builder()
      .name("name")
      .build();
    return new Field(selectField);
  }

  public Field username() {
    SelectField selectField = SelectField.builder()
      .name("username")
      .build();
    return new Field(selectField);
  }

}
