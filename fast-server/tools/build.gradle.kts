plugins {
  java
  `java-library`
}

val commonsLangVersion = "3.18.0"
val junitJupiterVersion = "5.9.1"
val lombokVersion = "1.18.34"



dependencies {

  api("org.apache.commons:commons-lang3:$commonsLangVersion")

  compileOnly("org.projectlombok:lombok:$lombokVersion")
  annotationProcessor("org.projectlombok:lombok:$lombokVersion")
  testCompileOnly("org.projectlombok:lombok:$lombokVersion")
  testAnnotationProcessor("org.projectlombok:lombok:$lombokVersion")

  testImplementation("io.vertx:vertx-junit5")
  testImplementation("org.junit.jupiter:junit-jupiter:$junitJupiterVersion")
}

java {
  sourceCompatibility = JavaVersion.VERSION_17
  targetCompatibility = JavaVersion.VERSION_17
}
