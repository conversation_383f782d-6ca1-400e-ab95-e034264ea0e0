package com.fast.tools.utils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateUtils {

  public static LocalDate parseDate(String dateStr) {
    if (dateStr != null) {
      if (dateStr.length() == 10) {
        return parseDate(dateStr, "yyyy-MM-dd");
      } else {
        return parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
      }
    } else {
      throw new IllegalArgumentException("Date and Patterns must not be null");
    }
  }

  public static LocalDate parseDate(String str, String... parsePatterns) {
    if (str == null || parsePatterns == null) {
      throw new IllegalArgumentException("Date and Patterns must not be null");
    }
    for (String pattern : parsePatterns) {
      try {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(str, dateTimeFormatter);
      } catch (final IllegalArgumentException | DateTimeException ignore) {
        // 忽略错误！
      }
    }
    throw new IllegalArgumentException("无法转换字符串:" + str + "到时间类型！");
  }

  public static LocalDateTime parseDateTime(String dateStr) {
    return parseDateTime(dateStr, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd");
  }

  public static LocalDateTime parseDateTime(String str, String... parsePatterns) {
    if (str == null || parsePatterns == null) {
      throw new IllegalArgumentException("Date and Patterns must not be null");
    }
    for (String pattern : parsePatterns) {
      try {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(str, dateTimeFormatter);
      } catch (final IllegalArgumentException | DateTimeException ignore) {
        // 忽略错误！
      }
    }
    throw new IllegalArgumentException("无法转换字符串:" + str + "到时间类型！");
  }


  public static Date convert(LocalDateTime localDateTime) {
    Instant instant = localDateTime.atZone(ZoneOffset.ofHours(8)).toInstant();
    // 获得 Date
    return Date.from(instant);
  }

  public static Date convert(LocalDate localDate) {
    Instant instant = localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant();
    // 获得 Date
    return Date.from(instant);
  }
}
