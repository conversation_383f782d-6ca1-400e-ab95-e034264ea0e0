package com.fast.tools.utils;


import java.lang.reflect.Array;
import java.time.temporal.TemporalAccessor;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: project-management
 * @description
 * @author: liunan
 * @create: 2019-11-12 09:56
 **/
public class FastObjectUtils {

    @SuppressWarnings("unchecked")
    public static <T> T cast(Object object) {
    return (T) object;
  }


  /**
     * 所有数组元素向上转型
     *
     * @param objs  转换前对象数组
     * @param clazz 转换后数组对象类型
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T[] cast(Object[] objs, Class<T> clazz) {
        int length = objs.length;
        if (length == 0) {
            return (T[]) new Object[0];
        }
        T[] newArr = (T[]) Array.newInstance(clazz, objs.length);
        for (int i = 0; i < length; i++) {
            newArr[i] = clazz.cast(objs[i]);
        }

        return newArr;
    }

    //---------------------------------------------------------------------
    // 对象类型判断
    //---------------------------------------------------------------------

    public static boolean isCollection(Object obj) {
        return obj instanceof Collection;
    }

    public static boolean isMap(Object obj) {
        return obj instanceof Map;
    }

    public static boolean isNumber(Object obj) {
        return obj instanceof Number;
    }

    public static boolean isBoolean(Object obj) {
        return obj instanceof Boolean;
    }

    public static boolean isEnum(Object obj) {
        return obj instanceof Enum;
    }

    public static boolean isDate(Object obj) {
        return obj instanceof TemporalAccessor || obj instanceof Date;
    }

    public static boolean isCharSequence(Object obj) {
        return obj instanceof CharSequence;
    }

    /**
     * 判断对象是否为八大基本类型包装类除外即(boolean, byte, char, short, int, long, float, and double)<br/>
     *
     * @param obj
     * @return
     */
    public static boolean isPrimitive(Object obj) {
        return obj != null && obj.getClass().isPrimitive();
    }

    /**
     * 判断对象是否为包装类或者非包装类的基本类型
     *
     * @param obj
     * @return
     */
    public static boolean isWrapperOrPrimitive(Object obj) {
        return isPrimitive(obj) || isNumber(obj) || isCharSequence(obj) || isBoolean(obj);
    }

    /**
     * 判断一个对象是否为数组
     *
     * @param obj
     * @return
     */
    public static boolean isArray(Object obj) {
        return obj != null && obj.getClass().isArray();
    }

    /**
     * 判断一个对象是否为基本类型数组即(int[], long[], boolean[], double[]....)
     *
     * @param obj
     * @return
     */
    public static boolean isPrimitiveArray(Object obj) {
        return isArray(obj) && obj.getClass().getComponentType().isPrimitive();
    }

    /**
     * 调用对象toString方法，如果对象为空则返回指定的默认字符串
     *
     * @param object     对象
     * @param defaultStr 对象为空时，返回的默认字符串
     * @return 对象toString方法or默认字符串
     */
    public static String toString(Object object, String defaultStr) {
        return object == null ? defaultStr : String.valueOf(object);
    }

    public static Object groupData(String fieldPath, String split, Object object, Object groupWithValue) {
        String[] fields = fieldPath.split(split);
        return groupData(fields, object, groupWithValue);
    }


    public static Object groupData(String[] fields, Object object, Object groupWithValue) {
        if (fields == null || fields.length == 0) {
            return object;
        }
        String field = fields[0];
        if (fields.length == 1 && object instanceof Collection<?> objectCollection) {
          return objectCollection.stream()
                    .filter(item -> item instanceof Map && groupWithValue != null
                            && groupWithValue.equals(((Map<?,?>) item).get(field))).collect(Collectors.toList());
        } else {
            fields = Arrays.copyOfRange(fields, 1, fields.length);
            if (object instanceof Map) {
                Map<String, Object> objMap = (Map<String, Object>) object;
                Object tempObject = objMap.get(field);
                if (tempObject != null) {
                    Object resultData = groupData(fields, tempObject, groupWithValue);
                    objMap.put(field, resultData);
                }
            } else if (object instanceof Collection) {
                for (Object item : (Collection<?>) object) {
                    if (item instanceof Map) {
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        Object tempObject = itemMap.get(field);
                        Object resultData = groupData(fields, tempObject, groupWithValue);
                        itemMap.put(field, resultData);
                    }
                }
            }
            return object;
        }
    }


    public static List<Map<String, Object>> mergeData(List<Map<String, Object>> targetObj, List<Map<String, Object>> sourceValue, String onlyMarkName) {
        // 相同markName 合并，剩下的也合并
        Map<Object, Map<String, Object>> targetObjMap = new HashMap<>();
        targetObj.forEach(item -> {
            Object onlyMark = item.get(onlyMarkName);
            if (onlyMark != null) {
                targetObjMap.put(onlyMark, item);
            }
        });
        sourceValue.forEach(item -> {
            Object onlyMark = item.get(onlyMarkName);
            if (onlyMark != null) {
                Map<String, Object> targetMap = targetObjMap.get(onlyMark);
                if (targetMap != null) {
                    targetMap.putAll(item);
                } else {
                    targetObj.add(item);
                }
            } else {
                targetObj.add(item);
            }
        });
//        targetObj.forEach(x -> {
//            Map<String, Object> itemSourceValue = sourceValue.stream()
//                    .filter(v -> x.get(onlyMarkName) != null && v.get(onlyMarkName).equals(x.get(onlyMarkName)))
//                    .findFirst().orElse(null);
//            if (itemSourceValue != null) {
//                x.putAll(itemSourceValue);
//            }
//        });
        return targetObj;
    }


    /**
     * targetObj 通过fields 找到的字段和 sourceValue 都是 List<Map<String,Object>> 格式
     * TODO 很多异常直接跳过，不适用情况很容易导致无效。有空再详细测试。或者重写。
     *
     * @param fields
     * @param targetObj
     * @param sourceValue
     * @return
     */
    public static Object mergeData(String[] fields, Object targetObj, List<Map<String, Object>> sourceValue, String onlyMarkName) {
        if (fields == null || fields.length == 0) {
            return targetObj;
        }
        String field = fields[0];
        if (fields.length == 1) {
            List<Map<String, Object>> targetListMap = (List<Map<String, Object>>) ((Map<?,?>) targetObj).get(field);
            List<Map<String, Object>> resultList = mergeData(targetListMap, sourceValue, onlyMarkName);
            // 如果Map，则直接覆盖。
            ((Map) targetObj).put(field, resultList);
            return targetObj;
        } else {
            fields = Arrays.copyOfRange(fields, 1, fields.length);
            if (targetObj instanceof Map) {
                Map<String, Object> targetObjMap = (Map<String, Object>) targetObj;
                Object tempTargetObject = targetObjMap.get(field);
                if (tempTargetObject != null) {
                    Object resultData = mergeData(fields, tempTargetObject, sourceValue, onlyMarkName);
                    targetObjMap.put(field, resultData);
                }
            } else if (targetObj instanceof Collection) {
                for (Object item : (Collection) targetObj) {
                    if (item instanceof Map) {
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        Object tempObject = itemMap.get(field);
                        Object resultData = mergeData(fields, tempObject, sourceValue, onlyMarkName);
                        itemMap.put(field, resultData);
                    }
                }
            }
            return targetObj;
        }
    }


}
