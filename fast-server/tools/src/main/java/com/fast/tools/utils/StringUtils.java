package com.fast.tools.utils;

import java.util.Set;

public class StringUtils extends org.apache.commons.lang3.StringUtils {

  public static final String UNDERLINE = "_";
  public static final String DASH = "-";


  // 首字母转小写
  public static String toLowerCaseFirstOne(String s) {
    if (s == null || s.isEmpty()) {
      return "";
    }
    char[] ch = s.toCharArray();
    if (ch[0] >= 'A' && ch[0] <= 'Z') {
      ch[0] = (char) (ch[0] + 32);
    }
    return new String(ch);
  }

  // 首字母转大写
  public static String toUpperCaseFirstOne(String s) {
    if (s == null || s.isEmpty()) {
      return "";
    }
    char[] ch = s.toCharArray();
    if (ch[0] >= 'a' && ch[0] <= 'z') {
      ch[0] = (char) (ch[0] - 32);
    }
    return new String(ch);

  }

  /**
   * 将驼峰式命名的字符串转换为下划线大写方式。如果转换前的驼峰式命名的字符串为空，则返回空字符串。</br>
   * 例如：HelloWorld->hello_world
   *
   * @param name 转换前的驼峰式命名的字符串
   * @return 转换后下划线大写方式命名的字符串
   */
  public static String camelConvertUnderline(String name) {
    StringBuilder result = new StringBuilder();
    if (name != null && name.length() > 0) {
      // 将第一个字符处理成大写
      result.append(name.substring(0, 1).toUpperCase());
      // 循环处理其余字符
      for (int i = 1; i < name.length(); i++) {
        String s = name.substring(i, i + 1);
        // 在大写字母前添加下划线
        if (!s.equals(UNDERLINE)
          && !s.equals(DASH)
          && s.equals(s.toUpperCase())
          && !Character.isDigit(s.charAt(0))
        ) {
          result.append(UNDERLINE);
        }
        // 其他字符直接转成大写
        result.append(s.toLowerCase());
      }
    }
    return result.toString().toLowerCase();
  }

  /**
   * 将下划线大写方式命名的字符串转换为驼峰式。如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。</br>
   * 例如：HELLO_WORLD->helloWorld
   *
   * @param name 转换前的下划线大写方式命名的字符串
   * @return 转换后的驼峰式命名的字符串
   */
  public static String underlineConvertCamel(String name) {
    StringBuilder result = new StringBuilder();
    // 快速检查
    if (name == null || name.isEmpty()) {
      // 没必要转换
      return "";
    } else if (!name.contains(UNDERLINE)) {
      // 不含下划线，仅将首字母小写
      return name.substring(0, 1).toLowerCase() + name.substring(1);
    }
    // 用下划线将原始字符串分割
    String[] camels = name.split(UNDERLINE);
    for (String camel : camels) {
      // 跳过原始字符串中开头、结尾的下换线或双重下划线
      if (camel.isEmpty()) {
        continue;
      }
      // 处理真正的驼峰片段
      if (result.length() == 0) {
        // 第一个驼峰片段，全部字母都小写
        result.append(camel.toLowerCase());
      } else {
        // 其他的驼峰片段，首字母大写
        result.append(camel.substring(0, 1).toUpperCase());
        result.append(camel.substring(1).toLowerCase());
      }
    }
    return result.toString();
  }

  /**
   * 下划线转驼峰首字符小写
   *
   * @param name
   * @return
   */
  public static String toCamelUpFirst(String name) {
    return toUpperCaseFirstOne(underlineConvertCamel(name));
  }

  public static Set<String> splitOfSet(String str, char splitChar) {
    return Set.of(split(str, splitChar));
  }

  public static Set<String> splitOfSet(String str, String regex) {
    return Set.of(split(str, regex));
  }

}
